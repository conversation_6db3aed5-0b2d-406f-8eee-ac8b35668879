"use strict";var ci=Object.defineProperty;var fi=(t,r)=>{for(var i in r)ci(t,i,{get:r[i],enumerable:!0})};var dt={};fi(dt,{Features:()=>$e,Polyfills:()=>Qe,__unstable__loadDesignSystem:()=>Gn,compile:()=>Hn,compileAst:()=>ui,default:()=>ze});var jt="4.1.3";var Ee=92,Le=47,Me=42,di=34,mi=39,gi=58,We=59,me=10,Re=32,Be=9,It=123,gt=125,wt=40,Ft=41,hi=91,vi=93,zt=45,ht=64,wi=33;function ge(t){t[0]==="\uFEFF"&&(t=t.slice(1)),t=t.replaceAll(`\r
`,`
`);let r=[],i=[],e=[],n=null,s=null,a="",f="",u;for(let c=0;c<t.length;c++){let g=t.charCodeAt(c);if(g===Ee)a+=t.slice(c,c+2),c+=1;else if(g===Le&&t.charCodeAt(c+1)===Me){let m=c;for(let v=c+2;v<t.length;v++)if(u=t.charCodeAt(v),u===Ee)v+=1;else if(u===Me&&t.charCodeAt(v+1)===Le){c=v+1;break}let h=t.slice(m,c+1);h.charCodeAt(2)===wi&&i.push(qe(h.slice(2,-2)))}else if(g===mi||g===di){let m=c;for(let h=c+1;h<t.length;h++)if(u=t.charCodeAt(h),u===Ee)h+=1;else if(u===g){c=h;break}else{if(u===We&&t.charCodeAt(h+1)===me)throw new Error(`Unterminated string: ${t.slice(m,h+1)+String.fromCharCode(g)}`);if(u===me)throw new Error(`Unterminated string: ${t.slice(m,h)+String.fromCharCode(g)}`)}a+=t.slice(m,c+1)}else{if((g===Re||g===me||g===Be)&&(u=t.charCodeAt(c+1))&&(u===Re||u===me||u===Be))continue;if(g===me){if(a.length===0)continue;u=a.charCodeAt(a.length-1),u!==Re&&u!==me&&u!==Be&&(a+=" ")}else if(g===zt&&t.charCodeAt(c+1)===zt&&a.length===0){let m="",h=c,v=-1;for(let A=c+2;A<t.length;A++)if(u=t.charCodeAt(A),u===Ee)A+=1;else if(u===Le&&t.charCodeAt(A+1)===Me){for(let b=A+2;b<t.length;b++)if(u=t.charCodeAt(b),u===Ee)b+=1;else if(u===Me&&t.charCodeAt(b+1)===Le){A=b+1;break}}else if(v===-1&&u===gi)v=a.length+A-h;else if(u===We&&m.length===0){a+=t.slice(h,A),c=A;break}else if(u===wt)m+=")";else if(u===hi)m+="]";else if(u===It)m+="}";else if((u===gt||t.length-1===A)&&m.length===0){c=A-1,a+=t.slice(h,A);break}else(u===Ft||u===vi||u===gt)&&m.length>0&&t[A]===m[m.length-1]&&(m=m.slice(0,-1));let w=vt(a,v);if(!w)throw new Error("Invalid custom property, expected a value");n?n.nodes.push(w):r.push(w),a=""}else if(g===We&&a.charCodeAt(0)===ht)s=Oe(a),n?n.nodes.push(s):r.push(s),a="",s=null;else if(g===We&&f[f.length-1]!==")"){let m=vt(a);if(!m)throw a.length===0?new Error("Unexpected semicolon"):new Error(`Invalid declaration: \`${a.trim()}\``);n?n.nodes.push(m):r.push(m),a=""}else if(g===It&&f[f.length-1]!==")")f+="}",s=M(a.trim()),n&&n.nodes.push(s),e.push(n),n=s,a="",s=null;else if(g===gt&&f[f.length-1]!==")"){if(f==="")throw new Error("Missing opening {");if(f=f.slice(0,-1),a.length>0)if(a.charCodeAt(0)===ht)s=Oe(a),n?n.nodes.push(s):r.push(s),a="",s=null;else{let h=a.indexOf(":");if(n){let v=vt(a,h);if(!v)throw new Error(`Invalid declaration: \`${a.trim()}\``);n.nodes.push(v)}}let m=e.pop()??null;m===null&&n&&r.push(n),n=m,a="",s=null}else if(g===wt)f+=")",a+="(";else if(g===Ft){if(f[f.length-1]!==")")throw new Error("Missing opening (");f=f.slice(0,-1),a+=")"}else{if(a.length===0&&(g===Re||g===me||g===Be))continue;a+=String.fromCharCode(g)}}}if(a.charCodeAt(0)===ht&&r.push(Oe(a)),f.length>0&&n){if(n.kind==="rule")throw new Error(`Missing closing } at ${n.selector}`);if(n.kind==="at-rule")throw new Error(`Missing closing } at ${n.name} ${n.params}`)}return i.length>0?i.concat(r):r}function Oe(t,r=[]){for(let i=5;i<t.length;i++){let e=t.charCodeAt(i);if(e===Re||e===wt){let n=t.slice(0,i).trim(),s=t.slice(i).trim();return j(n,s,r)}}return j(t.trim(),"",r)}function vt(t,r=t.indexOf(":")){if(r===-1)return null;let i=t.indexOf("!important",r+1);return l(t.slice(0,r).trim(),t.slice(r+1,i===-1?t.length:i).trim(),i!==-1)}function ue(t){if(arguments.length===0)throw new TypeError("`CSS.escape` requires an argument.");let r=String(t),i=r.length,e=-1,n,s="",a=r.charCodeAt(0);if(i===1&&a===45)return"\\"+r;for(;++e<i;){if(n=r.charCodeAt(e),n===0){s+="\uFFFD";continue}if(n>=1&&n<=31||n===127||e===0&&n>=48&&n<=57||e===1&&n>=48&&n<=57&&a===45){s+="\\"+n.toString(16)+" ";continue}if(n>=128||n===45||n===95||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122){s+=r.charAt(e);continue}s+="\\"+r.charAt(e)}return s}function he(t){return t.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,r=>r.length>2?String.fromCodePoint(Number.parseInt(r.slice(1).trim(),16)):r[1])}var Mt=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-decoration-color","--text-decoration-thickness","--text-indent","--text-shadow","--text-underline-offset"]]]);function Lt(t,r){return(Mt.get(r)??[]).some(i=>t===i||t.startsWith(`${i}-`))}var He=class{constructor(r=new Map,i=new Set([])){this.values=r;this.keyframes=i}prefix=null;add(r,i,e=0){if(r.endsWith("-*")){if(i!=="initial")throw new Error(`Invalid theme value \`${i}\` for namespace \`${r}\``);r==="--*"?this.values.clear():this.clearNamespace(r.slice(0,-2),0)}if(e&4){let n=this.values.get(r);if(n&&!(n.options&4))return}i==="initial"?this.values.delete(r):this.values.set(r,{value:i,options:e})}keysInNamespaces(r){let i=[];for(let e of r){let n=`${e}-`;for(let s of this.values.keys())s.startsWith(n)&&s.indexOf("--",2)===-1&&(Lt(s,e)||i.push(s.slice(n.length)))}return i}get(r){for(let i of r){let e=this.values.get(i);if(e)return e.value}return null}hasDefault(r){return(this.getOptions(r)&4)===4}getOptions(r){return r=he(this.#r(r)),this.values.get(r)?.options??0}entries(){return this.prefix?Array.from(this.values,r=>(r[0]=this.prefixKey(r[0]),r)):this.values.entries()}prefixKey(r){return this.prefix?`--${this.prefix}-${r.slice(2)}`:r}#r(r){return this.prefix?`--${r.slice(3+this.prefix.length)}`:r}clearNamespace(r,i){let e=Mt.get(r)??[];e:for(let n of this.values.keys())if(n.startsWith(r)){if(i!==0&&(this.getOptions(n)&i)!==i)continue;for(let s of e)if(n.startsWith(s))continue e;this.values.delete(n)}}#e(r,i){for(let e of i){let n=r!==null?`${e}-${r}`:e;if(!this.values.has(n))if(r!==null&&r.includes(".")){if(n=`${e}-${r.replaceAll(".","_")}`,!this.values.has(n))continue}else continue;if(!Lt(n,e))return n}return null}#t(r){let i=this.values.get(r);if(!i)return null;let e=null;return i.options&2&&(e=i.value),`var(${ue(this.prefixKey(r))}${e?`, ${e}`:""})`}markUsedVariable(r){let i=he(this.#r(r)),e=this.values.get(i);if(!e)return!1;let n=e.options&16;return e.options|=16,!n}resolve(r,i,e=0){let n=this.#e(r,i);if(!n)return null;let s=this.values.get(n);return(e|s.options)&1?s.value:this.#t(n)}resolveValue(r,i){let e=this.#e(r,i);return e?this.values.get(e).value:null}resolveWith(r,i,e=[]){let n=this.#e(r,i);if(!n)return null;let s={};for(let f of e){let u=`${n}${f}`,c=this.values.get(u);c&&(c.options&1?s[f]=c.value:s[f]=this.#t(u))}let a=this.values.get(n);return a.options&1?[a.value,s]:[this.#t(n),s]}namespace(r){let i=new Map,e=`${r}-`;for(let[n,s]of this.values)n===r?i.set(null,s.value):n.startsWith(`${e}-`)?i.set(n.slice(r.length),s.value):n.startsWith(e)&&i.set(n.slice(e.length),s.value);return i}addKeyframes(r){this.keyframes.add(r)}getKeyframes(){return Array.from(this.keyframes)}};var q=class extends Map{constructor(i){super();this.factory=i}get(i){let e=super.get(i);return e===void 0&&(e=this.factory(i,this),this.set(i,e)),e}};function yt(t){return{kind:"word",value:t}}function ki(t,r){return{kind:"function",value:t,nodes:r}}function yi(t){return{kind:"separator",value:t}}function ee(t,r,i=null){for(let e=0;e<t.length;e++){let n=t[e],s=!1,a=0,f=r(n,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(t.splice(e,1),a=0):u.length===1?(t[e]=u[0],a=1):(t.splice(e,1,...u),a=u.length):t[e]=u)}})??0;if(s){f===0?e--:e+=a-1;continue}if(f===2)return 2;if(f!==1&&n.kind==="function"&&ee(n.nodes,r,n)===2)return 2}}function J(t){let r="";for(let i of t)switch(i.kind){case"word":case"separator":{r+=i.value;break}case"function":r+=i.value+"("+J(i.nodes)+")"}return r}var Wt=92,bi=41,Bt=58,qt=44,xi=34,Ht=61,Gt=62,Yt=60,Jt=10,Ai=40,Ci=39,Qt=47,Zt=32,Xt=9;function H(t){t=t.replaceAll(`\r
`,`
`);let r=[],i=[],e=null,n="",s;for(let a=0;a<t.length;a++){let f=t.charCodeAt(a);switch(f){case Wt:{n+=t[a]+t[a+1],a++;break}case Bt:case qt:case Ht:case Gt:case Yt:case Jt:case Qt:case Zt:case Xt:{if(n.length>0){let m=yt(n);e?e.nodes.push(m):r.push(m),n=""}let u=a,c=a+1;for(;c<t.length&&(s=t.charCodeAt(c),!(s!==Bt&&s!==qt&&s!==Ht&&s!==Gt&&s!==Yt&&s!==Jt&&s!==Qt&&s!==Zt&&s!==Xt));c++);a=c-1;let g=yi(t.slice(u,c));e?e.nodes.push(g):r.push(g);break}case Ci:case xi:{let u=a;for(let c=a+1;c<t.length;c++)if(s=t.charCodeAt(c),s===Wt)c+=1;else if(s===f){a=c;break}n+=t.slice(u,a+1);break}case Ai:{let u=ki(n,[]);n="",e?e.nodes.push(u):r.push(u),i.push(u),e=u;break}case bi:{let u=i.pop();if(n.length>0){let c=yt(n);u.nodes.push(c),n=""}i.length>0?e=i[i.length-1]:e=null;break}default:n+=String.fromCharCode(f)}}return n.length>0&&r.push(yt(n)),r}function Ge(t){let r=[];return ee(H(t),i=>{if(!(i.kind!=="function"||i.value!=="var"))return ee(i.nodes,e=>{e.kind!=="word"||e.value[0]!=="-"||e.value[1]!=="-"||r.push(e.value)}),1}),r}var $i=64;function F(t,r=[]){return{kind:"rule",selector:t,nodes:r}}function j(t,r="",i=[]){return{kind:"at-rule",name:t,params:r,nodes:i}}function M(t,r=[]){return t.charCodeAt(0)===$i?Oe(t,r):F(t,r)}function l(t,r,i=!1){return{kind:"declaration",property:t,value:r,important:i}}function qe(t){return{kind:"comment",value:t}}function ne(t,r){return{kind:"context",context:t,nodes:r}}function D(t){return{kind:"at-root",nodes:t}}function I(t,r,i=[],e={}){for(let n=0;n<t.length;n++){let s=t[n],a=i[i.length-1]??null;if(s.kind==="context"){if(I(s.nodes,r,i,{...e,...s.context})===2)return 2;continue}i.push(s);let f=!1,u=0,c=r(s,{parent:a,context:e,path:i,replaceWith(g){f||(f=!0,Array.isArray(g)?g.length===0?(t.splice(n,1),u=0):g.length===1?(t[n]=g[0],u=1):(t.splice(n,1,...g),u=g.length):(t[n]=g,u=1))}})??0;if(i.pop(),f){c===0?n--:n+=u-1;continue}if(c===2)return 2;if(c!==1&&"nodes"in s){i.push(s);let g=I(s.nodes,r,i,e);if(i.pop(),g===2)return 2}}}function Ye(t,r,i=[],e={}){for(let n=0;n<t.length;n++){let s=t[n],a=i[i.length-1]??null;if(s.kind==="rule"||s.kind==="at-rule")i.push(s),Ye(s.nodes,r,i,e),i.pop();else if(s.kind==="context"){Ye(s.nodes,r,i,{...e,...s.context});continue}i.push(s),r(s,{parent:a,context:e,path:i,replaceWith(f){Array.isArray(f)?f.length===0?t.splice(n,1):f.length===1?t[n]=f[0]:t.splice(n,1,...f):t[n]=f,n+=f.length-1}}),i.pop()}}function ve(t,r,i=3){let e=[],n=new Set,s=new q(()=>new Set),a=new Set,f=new Set,u=[],c=[],g=new q(()=>new Set);function m(v,w,A={},b=0){if(v.kind==="declaration"){if(v.property==="--tw-sort"||v.value===void 0||v.value===null)return;if(A.theme&&v.property[0]==="-"&&v.property[1]==="-"){if(v.value==="initial"){v.value=void 0;return}A.keyframes||s.get(w).add(v)}if(v.value.includes("var("))if(A.theme&&v.property[0]==="-"&&v.property[1]==="-")for(let y of Ge(v.value))g.get(y).add(v.property);else r.trackUsedVariables(v.value);if(v.property==="animation")for(let y of er(v.value))f.add(y);if(i&2&&v.value.includes("color-mix(")){let y=H(v.value),S=!1;if(ee(y,(E,{replaceWith:P})=>{if(E.kind!=="function"||E.value!=="color-mix")return;let _=!1,L=!1;if(ee(E.nodes,(R,{replaceWith:G})=>{if(R.kind=="word"&&R.value.toLowerCase()==="currentcolor"){L=!0,S=!0;return}if(R.kind!=="function"||R.value!=="var")return;let z=R.nodes[0];if(!z||z.kind!=="word")return;S=!0;let B=r.theme.resolveValue(null,[z.value]);if(!B){_=!0;return}G({kind:"word",value:B})}),_||L){let R=E.nodes.findIndex(z=>z.kind==="separator"&&z.value.trim().includes(","));if(R===-1)return;let G=E.nodes.length>R?E.nodes[R+1]:null;if(!G)return;P(G)}else if(S){let R=E.nodes[2];R.kind==="word"&&(R.value==="oklab"||R.value==="oklch"||R.value==="lab"||R.value==="lch")&&(R.value="srgb")}}),S){let E={...v,value:J(y)},P=M("@supports (color: color-mix(in lab, red, red))",[v]);w.push(E,P);return}}w.push(v)}else if(v.kind==="rule")if(v.selector==="&")for(let y of v.nodes){let S=[];m(y,S,A,b+1),S.length>0&&w.push(...S)}else{let y={...v,nodes:[]};for(let S of v.nodes)m(S,y.nodes,A,b+1);y.nodes.length>0&&w.push(y)}else if(v.kind==="at-rule"&&v.name==="@property"&&b===0){if(n.has(v.params))return;if(i&1){let S=v.params,E=null,P=!1;for(let _ of v.nodes)_.kind==="declaration"&&(_.property==="initial-value"?E=_.value:_.property==="inherits"&&(P=_.value==="true"));P?u.push(l(S,E??"initial")):c.push(l(S,E??"initial"))}n.add(v.params);let y={...v,nodes:[]};for(let S of v.nodes)m(S,y.nodes,A,b+1);w.push(y)}else if(v.kind==="at-rule"){v.name==="@keyframes"&&(A={...A,keyframes:!0});let y={...v,nodes:[]};for(let S of v.nodes)m(S,y.nodes,A,b+1);v.name==="@keyframes"&&A.theme&&a.add(y),(y.nodes.length>0||y.name==="@layer"||y.name==="@charset"||y.name==="@custom-media"||y.name==="@namespace"||y.name==="@import")&&w.push(y)}else if(v.kind==="at-root")for(let y of v.nodes){let S=[];m(y,S,A,0);for(let E of S)e.push(E)}else if(v.kind==="context"){if(v.context.reference)return;for(let y of v.nodes)m(y,w,{...A,...v.context},b)}else v.kind==="comment"&&w.push(v)}let h=[];for(let v of t)m(v,h,{},0);e:for(let[v,w]of s)for(let A of w){if(tr(A.property,r.theme,g)){if(A.property.startsWith(r.theme.prefixKey("--animate-")))for(let S of er(A.value))f.add(S);continue}let y=v.indexOf(A);if(v.splice(y,1),v.length===0){let S=Vi(h,E=>E.kind==="rule"&&E.nodes===v);if(!S||S.length===0)continue e;S.unshift({kind:"at-root",nodes:h});do{let E=S.pop();if(!E)break;let P=S[S.length-1];if(!P||P.kind!=="at-root"&&P.kind!=="at-rule")break;let _=P.nodes.indexOf(E);if(_===-1)break;P.nodes.splice(_,1)}while(!0);continue e}}for(let v of a)if(!f.has(v.params)){let w=e.indexOf(v);e.splice(w,1)}if(h=h.concat(e),i&1){let v=[];if(u.length>0&&v.push(M(":root, :host",u)),c.length>0&&v.push(M("*, ::before, ::after, ::backdrop",c)),v.length>0){let w=h.findIndex(A=>!(A.kind==="comment"||A.kind==="at-rule"&&(A.name==="@charset"||A.name==="@import")));h.splice(w<0?h.length:w,0,j("@layer","properties",[])),h.push(M("@layer properties",[j("@supports","((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b))))",v)]))}}return h}function te(t){function r(e,n=0){let s="",a="  ".repeat(n);if(e.kind==="declaration")s+=`${a}${e.property}: ${e.value}${e.important?" !important":""};
`;else if(e.kind==="rule"){s+=`${a}${e.selector} {
`;for(let f of e.nodes)s+=r(f,n+1);s+=`${a}}
`}else if(e.kind==="at-rule"){if(e.nodes.length===0)return`${a}${e.name} ${e.params};
`;s+=`${a}${e.name}${e.params?` ${e.params} `:" "}{
`;for(let f of e.nodes)s+=r(f,n+1);s+=`${a}}
`}else if(e.kind==="comment")s+=`${a}/*${e.value}*/
`;else if(e.kind==="context"||e.kind==="at-root")return"";return s}let i="";for(let e of t){let n=r(e);n!==""&&(i+=n)}return i}function Vi(t,r){let i=[];return I(t,(e,{path:n})=>{if(r(e))return i=[...n],2}),i}function tr(t,r,i,e=new Set){if(e.has(t)||(e.add(t),r.getOptions(t)&24))return!0;{let s=i.get(t)??[];for(let a of s)if(tr(a,r,i,e))return!0}return!1}function er(t){return t.split(/[\s,]+/)}var bt=["calc","min","max","clamp","mod","rem","sin","cos","tan","asin","acos","atan","atan2","pow","sqrt","hypot","log","exp","round"],Ze=["anchor-size"],rr=new RegExp(`(${Ze.join("|")})\\(`,"g");function Pe(t){return t.indexOf("(")!==-1&&bt.some(r=>t.includes(`${r}(`))}function ir(t){if(!bt.some(n=>t.includes(n)))return t;let r=!1;Ze.some(n=>t.includes(n))&&(rr.lastIndex=0,t=t.replace(rr,(n,s)=>(r=!0,`$${Ze.indexOf(s)}$(`)));let i="",e=[];for(let n=0;n<t.length;n++){let s=t[n];if(s==="("){i+=s;let a=n;for(let u=n-1;u>=0;u--){let c=t.charCodeAt(u);if(c>=48&&c<=57)a=u;else if(c>=97&&c<=122)a=u;else break}let f=t.slice(a,n);if(bt.includes(f)){e.unshift(!0);continue}else if(e[0]&&f===""){e.unshift(!0);continue}e.unshift(!1);continue}else if(s===")")i+=s,e.shift();else if(s===","&&e[0]){i+=", ";continue}else{if(s===" "&&e[0]&&i[i.length-1]===" ")continue;if((s==="+"||s==="*"||s==="/"||s==="-")&&e[0]){let a=i.trimEnd(),f=a[a.length-1];if(f==="+"||f==="*"||f==="/"||f==="-"){i+=s;continue}else if(f==="("||f===","){i+=s;continue}else t[n-1]===" "?i+=`${s} `:i+=` ${s} `}else if(e[0]&&t.startsWith("to-zero",n)){let a=n;n+=7,i+=t.slice(a,n+1)}else i+=s}}return r?i.replace(/\$(\d+)\$/g,(n,s)=>Ze[s]??n):i}function ce(t){if(t.indexOf("(")===-1)return Ce(t);let r=H(t);return xt(r),t=J(r),t=ir(t),t}function Ce(t,r=!1){let i="";for(let e=0;e<t.length;e++){let n=t[e];n==="\\"&&t[e+1]==="_"?(i+="_",e+=1):n==="_"&&!r?i+=" ":i+=n}return i}function xt(t){for(let r of t)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=Ce(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=Ce(r.value);for(let i=0;i<r.nodes.length;i++){if(i==0&&r.nodes[i].kind==="word"){r.nodes[i].value=Ce(r.nodes[i].value,!0);continue}xt([r.nodes[i]])}break}r.value=Ce(r.value),xt(r.nodes);break}case"separator":case"word":{r.value=Ce(r.value);break}default:Si(r)}}function Si(t){throw new Error(`Unexpected value: ${t}`)}var At=new Uint8Array(256);function fe(t){let r=0,i=t.length;for(let e=0;e<i;e++){let n=t.charCodeAt(e);switch(n){case 92:e+=1;break;case 39:case 34:for(;++e<i;){let s=t.charCodeAt(e);if(s===92){e+=1;continue}if(s===n)break}break;case 40:At[r]=41,r++;break;case 91:At[r]=93,r++;break;case 123:break;case 93:case 125:case 41:if(r===0)return!1;r>0&&n===At[r-1]&&r--;break;case 59:if(r===0)return!1;break}}return!0}var Xe=new Uint8Array(256);function K(t,r){let i=0,e=[],n=0,s=t.length,a=r.charCodeAt(0);for(let f=0;f<s;f++){let u=t.charCodeAt(f);if(i===0&&u===a){e.push(t.slice(n,f)),n=f+1;continue}switch(u){case 92:f+=1;break;case 39:case 34:for(;++f<s;){let c=t.charCodeAt(f);if(c===92){f+=1;continue}if(c===u)break}break;case 40:Xe[i]=41,i++;break;case 91:Xe[i]=93,i++;break;case 123:Xe[i]=125,i++;break;case 93:case 125:case 41:i>0&&u===Xe[i-1]&&i--;break}}return e.push(t.slice(n)),e}var Ti=58,nr=45,or=97,lr=122;function*ar(t,r){let i=K(t,":");if(r.theme.prefix){if(i.length===1||i[0]!==r.theme.prefix)return null;i.shift()}let e=i.pop(),n=[];for(let m=i.length-1;m>=0;--m){let h=r.parseVariant(i[m]);if(h===null)return;n.push(h)}let s=!1;e[e.length-1]==="!"?(s=!0,e=e.slice(0,-1)):e[0]==="!"&&(s=!0,e=e.slice(1)),r.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:n,important:s,raw:t});let[a,f=null,u]=K(e,"/");if(u)return;let c=f===null?null:Ct(f);if(f!==null&&c===null)return;if(a[0]==="["){if(a[a.length-1]!=="]")return;let m=a.charCodeAt(1);if(m!==nr&&!(m>=or&&m<=lr))return;a=a.slice(1,-1);let h=a.indexOf(":");if(h===-1||h===0||h===a.length-1)return;let v=a.slice(0,h),w=ce(a.slice(h+1));if(!fe(w))return;yield{kind:"arbitrary",property:v,value:w,modifier:c,variants:n,important:s,raw:t};return}let g;if(a[a.length-1]==="]"){let m=a.indexOf("-[");if(m===-1)return;let h=a.slice(0,m);if(!r.utilities.has(h,"functional"))return;let v=a.slice(m+1);g=[[h,v]]}else if(a[a.length-1]===")"){let m=a.indexOf("-(");if(m===-1)return;let h=a.slice(0,m);if(!r.utilities.has(h,"functional"))return;let v=a.slice(m+2,-1),w=K(v,":"),A=null;if(w.length===2&&(A=w[0],v=w[1]),v[0]!=="-"&&v[1]!=="-")return;g=[[h,A===null?`[var(${v})]`:`[${A}:var(${v})]`]]}else g=ur(a,m=>r.utilities.has(m,"functional"));for(let[m,h]of g){let v={kind:"functional",root:m,modifier:c,value:null,variants:n,important:s,raw:t};if(h===null){yield v;continue}{let w=h.indexOf("[");if(w!==-1){if(h[h.length-1]!=="]")return;let b=ce(h.slice(w+1,-1));if(!fe(b))continue;let y="";for(let S=0;S<b.length;S++){let E=b.charCodeAt(S);if(E===Ti){y=b.slice(0,S),b=b.slice(S+1);break}if(!(E===nr||E>=or&&E<=lr))break}if(b.length===0||b.trim().length===0)continue;v.value={kind:"arbitrary",dataType:y||null,value:b}}else{let b=f===null||v.modifier?.kind==="arbitrary"?null:`${h}/${f}`;v.value={kind:"named",value:h,fraction:b}}}yield v}}function Ct(t){if(t[0]==="["&&t[t.length-1]==="]"){let r=ce(t.slice(1,-1));return!fe(r)||r.length===0||r.trim().length===0?null:{kind:"arbitrary",value:r}}if(t[0]==="("&&t[t.length-1]===")"){let r=ce(t.slice(1,-1));return!fe(r)||r.length===0||r.trim().length===0||r[0]!=="-"&&r[1]!=="-"?null:{kind:"arbitrary",value:`var(${r})`}}return{kind:"named",value:t}}function sr(t,r){if(t[0]==="["&&t[t.length-1]==="]"){if(t[1]==="@"&&t.includes("&"))return null;let i=ce(t.slice(1,-1));if(!fe(i)||i.length===0||i.trim().length===0)return null;let e=i[0]===">"||i[0]==="+"||i[0]==="~";return!e&&i[0]!=="@"&&!i.includes("&")&&(i=`&:is(${i})`),{kind:"arbitrary",selector:i,relative:e}}{let[i,e=null,n]=K(t,"/");if(n)return null;let s=ur(i,a=>r.variants.has(a));for(let[a,f]of s)switch(r.variants.kind(a)){case"static":return f!==null||e!==null?null:{kind:"static",root:a};case"functional":{let u=e===null?null:Ct(e);if(e!==null&&u===null)return null;if(f===null)return{kind:"functional",root:a,modifier:u,value:null};if(f[f.length-1]==="]"){if(f[0]!=="[")continue;let c=ce(f.slice(1,-1));return!fe(c)||c.length===0||c.trim().length===0?null:{kind:"functional",root:a,modifier:u,value:{kind:"arbitrary",value:c}}}if(f[f.length-1]===")"){if(f[0]!=="(")continue;let c=ce(f.slice(1,-1));return!fe(c)||c.length===0||c.trim().length===0||c[0]!=="-"&&c[1]!=="-"?null:{kind:"functional",root:a,modifier:u,value:{kind:"arbitrary",value:`var(${c})`}}}return{kind:"functional",root:a,modifier:u,value:{kind:"named",value:f}}}case"compound":{if(f===null)return null;let u=r.parseVariant(f);if(u===null||!r.variants.compoundsWith(a,u))return null;let c=e===null?null:Ct(e);return e!==null&&c===null?null:{kind:"compound",root:a,modifier:c,variant:u}}}}return null}function*ur(t,r){r(t)&&(yield[t,null]);let i=t.lastIndexOf("-");if(i===-1){t[0]==="@"&&r("@")&&(yield["@",t.slice(1)]);return}do{let e=t.slice(0,i);if(r(e)){let n=[e,t.slice(i+1)];if(n[1]==="")break;yield n}i=t.lastIndexOf("-",i-1)}while(i>0)}function we(t,r,i){if(t===r)return 0;let e=t.indexOf("("),n=r.indexOf("("),s=e===-1?t.replace(/[\d.]+/g,""):t.slice(0,e),a=n===-1?r.replace(/[\d.]+/g,""):r.slice(0,n),f=(s===a?0:s<a?-1:1)||(i==="asc"?parseInt(t)-parseInt(r):parseInt(r)-parseInt(t));return Number.isNaN(f)?t<r?-1:1:f}var Ei=new Set(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen","transparent","currentcolor","canvas","canvastext","linktext","visitedtext","activetext","buttonface","buttontext","buttonborder","field","fieldtext","highlight","highlighttext","selecteditem","selecteditemtext","mark","marktext","graytext","accentcolor","accentcolortext"]),Ri=/^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i;function cr(t){return t.charCodeAt(0)===35||Ri.test(t)||Ei.has(t.toLowerCase())}var Oi={color:cr,length:et,percentage:Nt,ratio:Bi,number:pr,integer:T,url:fr,position:Gi,"bg-size":Yi,"line-width":_i,image:Di,"family-name":Ii,"generic-name":ji,"absolute-size":Fi,"relative-size":zi,angle:Zi,vector:en};function W(t,r){if(t.startsWith("var("))return null;for(let i of r)if(Oi[i]?.(t))return i;return null}var Pi=/^url\(.*\)$/;function fr(t){return Pi.test(t)}function _i(t){return K(t," ").every(r=>et(r)||pr(r)||r==="thin"||r==="medium"||r==="thick")}var Ki=/^(?:element|image|cross-fade|image-set)\(/,Ui=/^(repeating-)?(conic|linear|radial)-gradient\(/;function Di(t){let r=0;for(let i of K(t,","))if(!i.startsWith("var(")){if(fr(i)){r+=1;continue}if(Ui.test(i)){r+=1;continue}if(Ki.test(i)){r+=1;continue}return!1}return r>0}function ji(t){return t==="serif"||t==="sans-serif"||t==="monospace"||t==="cursive"||t==="fantasy"||t==="system-ui"||t==="ui-serif"||t==="ui-sans-serif"||t==="ui-monospace"||t==="ui-rounded"||t==="math"||t==="emoji"||t==="fangsong"}function Ii(t){let r=0;for(let i of K(t,",")){let e=i.charCodeAt(0);if(e>=48&&e<=57)return!1;i.startsWith("var(")||(r+=1)}return r>0}function Fi(t){return t==="xx-small"||t==="x-small"||t==="small"||t==="medium"||t==="large"||t==="x-large"||t==="xx-large"||t==="xxx-large"}function zi(t){return t==="larger"||t==="smaller"}var le=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,Li=new RegExp(`^${le.source}$`);function pr(t){return Li.test(t)||Pe(t)}var Mi=new RegExp(`^${le.source}%$`);function Nt(t){return Mi.test(t)||Pe(t)}var Wi=new RegExp(`^${le.source}s*/s*${le.source}$`);function Bi(t){return Wi.test(t)||Pe(t)}var qi=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],Hi=new RegExp(`^${le.source}(${qi.join("|")})$`);function et(t){return Hi.test(t)||Pe(t)}function Gi(t){let r=0;for(let i of K(t," ")){if(i==="center"||i==="top"||i==="right"||i==="bottom"||i==="left"){r+=1;continue}if(!i.startsWith("var(")){if(et(i)||Nt(i)){r+=1;continue}return!1}}return r>0}function Yi(t){let r=0;for(let i of K(t,",")){if(i==="cover"||i==="contain"){r+=1;continue}let e=K(i," ");if(e.length!==1&&e.length!==2)return!1;if(e.every(n=>n==="auto"||et(n)||Nt(n))){r+=1;continue}}return r>0}var Ji=["deg","rad","grad","turn"],Qi=new RegExp(`^${le.source}(${Ji.join("|")})$`);function Zi(t){return Qi.test(t)}var Xi=new RegExp(`^${le.source} +${le.source} +${le.source}$`);function en(t){return Xi.test(t)}function T(t){let r=Number(t);return Number.isInteger(r)&&r>=0&&String(r)===String(t)}function $t(t){let r=Number(t);return Number.isInteger(r)&&r>0&&String(r)===String(t)}function ke(t){return dr(t,.25)}function tt(t){return dr(t,.25)}function dr(t,r){let i=Number(t);return i>=0&&i%r===0&&String(i)===String(t)}var tn=new Set(["inset","inherit","initial","revert","unset"]),mr=/^-?(\d+|\.\d+)(.*?)$/g;function _e(t,r){return K(t,",").map(e=>{e=e.trim();let n=K(e," ").filter(c=>c.trim()!==""),s=null,a=null,f=null;for(let c of n)tn.has(c)||(mr.test(c)?(a===null?a=c:f===null&&(f=c),mr.lastIndex=0):s===null&&(s=c));if(a===null||f===null)return e;let u=r(s??"currentcolor");return s!==null?e.replace(s,u):`${e} ${u}`}).join(", ")}var rn=/^-?[a-z][a-zA-Z0-9/%._-]*$/,nn=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,it=["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],Vt=class{utilities=new q(()=>[]);completions=new Map;static(r,i){this.utilities.get(r).push({kind:"static",compileFn:i})}functional(r,i,e){this.utilities.get(r).push({kind:"functional",compileFn:i,options:e})}has(r,i){return this.utilities.has(r)&&this.utilities.get(r).some(e=>e.kind===i)}get(r){return this.utilities.has(r)?this.utilities.get(r):[]}getCompletions(r){return this.completions.get(r)?.()??[]}suggest(r,i){this.completions.set(r,i)}keys(r){let i=[];for(let[e,n]of this.utilities.entries())for(let s of n)if(s.kind===r){i.push(e);break}return i}};function C(t,r,i){return j("@property",t,[l("syntax",i?`"${i}"`:'"*"'),l("inherits","false"),...r?[l("initial-value",r)]:[]])}function Y(t,r){if(r===null)return t;let i=Number(r);return Number.isNaN(i)||(r=`${i*100}%`),`color-mix(in oklab, ${t} ${r}, transparent)`}function hr(t,r){let i=Number(r);return Number.isNaN(i)||(r=`${i*100}%`),`oklab(from ${t} l a b / ${r})`}function Q(t,r,i){if(!r)return t;if(r.kind==="arbitrary")return Y(t,r.value);let e=i.resolve(r.value,["--opacity"]);return e?Y(t,e):tt(r.value)?Y(t,`${r.value}%`):null}function Z(t,r,i){let e=null;switch(t.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentcolor";break}default:{e=r.resolve(t.value.value,i);break}}return e?Q(e,t.modifier,r):null}function vr(t){let r=new Vt;function i(o,p){let d=/(\d+)_(\d+)/g;function*x(N){for(let O of t.keysInNamespaces(N))yield O.replace(d,($,V,U)=>`${V}.${U}`)}let k=["1/2","1/3","2/3","1/4","2/4","3/4","1/5","2/5","3/5","4/5","1/6","2/6","3/6","4/6","5/6","1/12","2/12","3/12","4/12","5/12","6/12","7/12","8/12","9/12","10/12","11/12"];r.suggest(o,()=>{let N=[];for(let O of p()){if(typeof O=="string"){N.push({values:[O],modifiers:[]});continue}let $=[...O.values??[],...x(O.valueThemeKeys??[])],V=[...O.modifiers??[],...x(O.modifierThemeKeys??[])];O.supportsFractions&&$.push(...k),O.hasDefaultValue&&$.unshift(null),N.push({supportsNegative:O.supportsNegative,values:$,modifiers:V})}return N})}function e(o,p){r.static(o,()=>p.map(d=>typeof d=="function"?d():l(d[0],d[1])))}function n(o,p){function d({negative:x}){return k=>{let N=null,O=null;if(k.value)if(k.value.kind==="arbitrary"){if(k.modifier)return;N=k.value.value,O=k.value.dataType}else{if(N=t.resolve(k.value.fraction??k.value.value,p.themeKeys??[]),N===null&&p.supportsFractions&&k.value.fraction){let[$,V]=K(k.value.fraction,"/");if(!T($)||!T(V))return;N=`calc(${k.value.fraction} * 100%)`}if(N===null&&x&&p.handleNegativeBareValue){if(N=p.handleNegativeBareValue(k.value),!N?.includes("/")&&k.modifier)return;if(N!==null)return p.handle(N,null)}if(N===null&&p.handleBareValue&&(N=p.handleBareValue(k.value),!N?.includes("/")&&k.modifier))return}else{if(k.modifier)return;N=p.defaultValue!==void 0?p.defaultValue:t.resolve(null,p.themeKeys??[])}if(N!==null)return p.handle(x?`calc(${N} * -1)`:N,O)}}p.supportsNegative&&r.functional(`-${o}`,d({negative:!0})),r.functional(o,d({negative:!1})),i(o,()=>[{supportsNegative:p.supportsNegative,valueThemeKeys:p.themeKeys??[],hasDefaultValue:p.defaultValue!==void 0&&p.defaultValue!==null,supportsFractions:p.supportsFractions}])}function s(o,p){r.functional(o,d=>{if(!d.value)return;let x=null;if(d.value.kind==="arbitrary"?(x=d.value.value,x=Q(x,d.modifier,t)):x=Z(d,t,p.themeKeys),x!==null)return p.handle(x)}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:p.themeKeys,modifiers:Array.from({length:21},(d,x)=>`${x*5}`)}])}function a(o,p,d,{supportsNegative:x=!1,supportsFractions:k=!1}={}){x&&r.static(`-${o}-px`,()=>d("-1px")),r.static(`${o}-px`,()=>d("1px")),n(o,{themeKeys:p,supportsFractions:k,supportsNegative:x,defaultValue:null,handleBareValue:({value:N})=>{let O=t.resolve(null,["--spacing"]);return!O||!ke(N)?null:`calc(${O} * ${N})`},handleNegativeBareValue:({value:N})=>{let O=t.resolve(null,["--spacing"]);return!O||!ke(N)?null:`calc(${O} * -${N})`},handle:d}),i(o,()=>[{values:t.get(["--spacing"])?it:[],supportsNegative:x,supportsFractions:k,valueThemeKeys:p}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[o,p]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${o}-auto`,[[p,"auto"]]),e(`${o}-full`,[[p,"100%"]]),e(`-${o}-full`,[[p,"-100%"]]),a(o,["--inset","--spacing"],d=>[l(p,d)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),e("z-auto",[["z-index","auto"]]),n("z",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--z-index"],handle:o=>[l("z-index",o)]}),i("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),e("order-first",[["order","-9999"]]),e("order-last",[["order","9999"]]),e("order-none",[["order","0"]]),n("order",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--order"],handle:o=>[l("order",o)]}),i("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:["--order"]}]),e("col-auto",[["grid-column","auto"]]),n("col",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-column"],handle:o=>[l("grid-column",o)]}),e("col-span-full",[["grid-column","1 / -1"]]),n("col-span",{handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("grid-column",`span ${o} / span ${o}`)]}),e("col-start-auto",[["grid-column-start","auto"]]),n("col-start",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-column-start"],handle:o=>[l("grid-column-start",o)]}),e("col-end-auto",[["grid-column-end","auto"]]),n("col-end",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-column-end"],handle:o=>[l("grid-column-end",o)]}),i("col-span",()=>[{values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:[]}]),i("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-column-start"]}]),i("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-column-end"]}]),e("row-auto",[["grid-row","auto"]]),n("row",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-row"],handle:o=>[l("grid-row",o)]}),e("row-span-full",[["grid-row","1 / -1"]]),n("row-span",{themeKeys:[],handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("grid-row",`span ${o} / span ${o}`)]}),e("row-start-auto",[["grid-row-start","auto"]]),n("row-start",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-row-start"],handle:o=>[l("grid-row-start",o)]}),e("row-end-auto",[["grid-row-end","auto"]]),n("row-end",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-row-end"],handle:o=>[l("grid-row-end",o)]}),i("row-span",()=>[{values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:[]}]),i("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-row-start"]}]),i("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[o,p]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${o}-auto`,[[p,"auto"]]),a(o,["--margin","--spacing"],d=>[l(p,d)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),e("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),n("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("overflow","hidden"),l("display","-webkit-box"),l("-webkit-box-orient","vertical"),l("-webkit-line-clamp",o)]}),i("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),e("aspect-auto",[["aspect-ratio","auto"]]),e("aspect-square",[["aspect-ratio","1 / 1"]]),n("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:o})=>{if(o===null)return null;let[p,d]=K(o,"/");return!T(p)||!T(d)?null:o},handle:o=>[l("aspect-ratio",o)]});for(let[o,p]of[["auto","auto"],["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${o}`,[["--tw-sort","size"],["width",p],["height",p]]),e(`w-${o}`,[["width",p]]),e(`h-${o}`,[["height",p]]),e(`min-w-${o}`,[["min-width",p]]),e(`min-h-${o}`,[["min-height",p]]),o!=="auto"&&(e(`max-w-${o}`,[["max-width",p]]),e(`max-h-${o}`,[["max-height",p]]));e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("max-w-none",[["max-width","none"]]),e("max-h-none",[["max-height","none"]]),a("size",["--size","--spacing"],o=>[l("--tw-sort","size"),l("width",o),l("height",o)],{supportsFractions:!0});for(let[o,p,d]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])a(o,p,x=>[l(d,x)],{supportsFractions:!0});r.static("container",()=>{let o=[...t.namespace("--breakpoint").values()];o.sort((d,x)=>we(d,x,"asc"));let p=[l("--tw-sort","--tw-container-component"),l("width","100%")];for(let d of o)p.push(j("@media",`(width >= ${d})`,[l("max-width",d)]));return p}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),r.functional("flex",o=>{if(o.value){if(o.value.kind==="arbitrary")return o.modifier?void 0:[l("flex",o.value.value)];if(o.value.fraction){let[p,d]=K(o.value.fraction,"/");return!T(p)||!T(d)?void 0:[l("flex",`calc(${o.value.fraction} * 100%)`)]}if(T(o.value.value))return o.modifier?void 0:[l("flex",o.value.value)]}}),i("flex",()=>[{supportsFractions:!0}]),n("shrink",{defaultValue:"1",handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("flex-shrink",o)]}),n("grow",{defaultValue:"1",handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("flex-grow",o)]}),i("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),i("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),a("basis",["--flex-basis","--spacing","--container"],o=>[l("flex-basis",o)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let f=()=>D([C("--tw-border-spacing-x","0","<length>"),C("--tw-border-spacing-y","0","<length>")]);a("border-spacing",["--border-spacing","--spacing"],o=>[f(),l("--tw-border-spacing-x",o),l("--tw-border-spacing-y",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-x",["--border-spacing","--spacing"],o=>[f(),l("--tw-border-spacing-x",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-y",["--border-spacing","--spacing"],o=>[f(),l("--tw-border-spacing-y",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),e("origin-center",[["transform-origin","center"]]),e("origin-top",[["transform-origin","top"]]),e("origin-top-right",[["transform-origin","top right"]]),e("origin-right",[["transform-origin","right"]]),e("origin-bottom-right",[["transform-origin","bottom right"]]),e("origin-bottom",[["transform-origin","bottom"]]),e("origin-bottom-left",[["transform-origin","bottom left"]]),e("origin-left",[["transform-origin","left"]]),e("origin-top-left",[["transform-origin","top left"]]),n("origin",{themeKeys:["--transform-origin"],handle:o=>[l("transform-origin",o)]}),e("perspective-origin-center",[["perspective-origin","center"]]),e("perspective-origin-top",[["perspective-origin","top"]]),e("perspective-origin-top-right",[["perspective-origin","top right"]]),e("perspective-origin-right",[["perspective-origin","right"]]),e("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),e("perspective-origin-bottom",[["perspective-origin","bottom"]]),e("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),e("perspective-origin-left",[["perspective-origin","left"]]),e("perspective-origin-top-left",[["perspective-origin","top left"]]),n("perspective-origin",{themeKeys:["--perspective-origin"],handle:o=>[l("perspective-origin",o)]}),e("perspective-none",[["perspective","none"]]),n("perspective",{themeKeys:["--perspective"],handle:o=>[l("perspective",o)]});let u=()=>D([C("--tw-translate-x","0"),C("--tw-translate-y","0"),C("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[u,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[u,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a("translate",["--translate","--spacing"],o=>[u(),l("--tw-translate-x",o),l("--tw-translate-y",o),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let o of["x","y"])e(`-translate-${o}-full`,[u,[`--tw-translate-${o}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${o}-full`,[u,[`--tw-translate-${o}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a(`translate-${o}`,["--translate","--spacing"],p=>[u(),l(`--tw-translate-${o}`,p),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});a("translate-z",["--translate","--spacing"],o=>[u(),l("--tw-translate-z",o),l("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("translate-3d",[u,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let c=()=>D([C("--tw-scale-x","1"),C("--tw-scale-y","1"),C("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function g({negative:o}){return p=>{if(!p.value||p.modifier)return;let d;return p.value.kind==="arbitrary"?(d=p.value.value,[l("scale",d)]):(d=t.resolve(p.value.value,["--scale"]),!d&&T(p.value.value)&&(d=`${p.value.value}%`),d?(d=o?`calc(${d} * -1)`:d,[c(),l("--tw-scale-x",d),l("--tw-scale-y",d),l("--tw-scale-z",d),l("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}r.functional("-scale",g({negative:!0})),r.functional("scale",g({negative:!1})),i("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let o of["x","y","z"])n(`scale-${o}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:p})=>T(p)?`${p}%`:null,handle:p=>[c(),l(`--tw-scale-${o}`,p),l("scale",`var(--tw-scale-x) var(--tw-scale-y)${o==="z"?" var(--tw-scale-z)":""}`)]}),i(`scale-${o}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[c,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function m({negative:o}){return p=>{if(!p.value||p.modifier)return;let d;if(p.value.kind==="arbitrary"){d=p.value.value;let x=p.value.dataType??W(d,["angle","vector"]);if(x==="vector")return[l("rotate",`${d} var(--tw-rotate)`)];if(x!=="angle")return[l("rotate",d)]}else if(d=t.resolve(p.value.value,["--rotate"]),!d&&T(p.value.value)&&(d=`${p.value.value}deg`),!d)return;return[l("rotate",o?`calc(${d} * -1)`:d)]}}r.functional("-rotate",m({negative:!0})),r.functional("rotate",m({negative:!1})),i("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let o=["var(--tw-rotate-x)","var(--tw-rotate-y)","var(--tw-rotate-z)","var(--tw-skew-x)","var(--tw-skew-y)"].join(" "),p=()=>D([C("--tw-rotate-x","rotateX(0)"),C("--tw-rotate-y","rotateY(0)"),C("--tw-rotate-z","rotateZ(0)"),C("--tw-skew-x","skewX(0)"),C("--tw-skew-y","skewY(0)")]);for(let d of["x","y","z"])n(`rotate-${d}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:x})=>T(x)?`${x}deg`:null,handle:x=>[p(),l(`--tw-rotate-${d}`,`rotate${d.toUpperCase()}(${x})`),l("transform",o)]}),i(`rotate-${d}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);n("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:d})=>T(d)?`${d}deg`:null,handle:d=>[p(),l("--tw-skew-x",`skewX(${d})`),l("--tw-skew-y",`skewY(${d})`),l("transform",o)]}),n("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:d})=>T(d)?`${d}deg`:null,handle:d=>[p(),l("--tw-skew-x",`skewX(${d})`),l("transform",o)]}),n("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:d})=>T(d)?`${d}deg`:null,handle:d=>[p(),l("--tw-skew-y",`skewY(${d})`),l("transform",o)]}),i("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),r.functional("transform",d=>{if(d.modifier)return;let x=null;if(d.value?d.value.kind==="arbitrary"&&(x=d.value.value):x=o,x!==null)return[p(),l("transform",x)]}),i("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",o]]),e("transform-gpu",[["transform",`translateZ(0) ${o}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let o of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${o}`,[["cursor",o]]);n("cursor",{themeKeys:["--cursor"],handle:o=>[l("cursor",o)]});for(let o of["auto","none","manipulation"])e(`touch-${o}`,[["touch-action",o]]);let h=()=>D([C("--tw-pan-x"),C("--tw-pan-y"),C("--tw-pinch-zoom")]);for(let o of["x","left","right"])e(`touch-pan-${o}`,[h,["--tw-pan-x",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["y","up","down"])e(`touch-pan-${o}`,[h,["--tw-pan-y",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[h,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["none","text","all","auto"])e(`select-${o}`,[["-webkit-user-select",o],["user-select",o]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let v=()=>D([C("--tw-scroll-snap-strictness","proximity","*")]);for(let o of["x","y","both"])e(`snap-${o}`,[v,["scroll-snap-type",`${o} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[v,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[v,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[o,p]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])a(o,["--scroll-margin","--spacing"],d=>[l(p,d)],{supportsNegative:!0});for(let[o,p]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])a(o,["--scroll-padding","--spacing"],d=>[l(p,d)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),e("list-none",[["list-style-type","none"]]),e("list-disc",[["list-style-type","disc"]]),e("list-decimal",[["list-style-type","decimal"]]),n("list",{themeKeys:["--list-style-type"],handle:o=>[l("list-style-type",o)]}),e("list-image-none",[["list-style-image","none"]]),n("list-image",{themeKeys:["--list-style-image"],handle:o=>[l("list-style-image",o)]}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),e("columns-auto",[["columns","auto"]]),n("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("columns",o)]}),i("columns",()=>[{values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:["--columns","--container"]}]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${o}`,[["break-before",o]]);for(let o of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${o}`,[["break-inside",o]]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${o}`,[["break-after",o]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),e("auto-cols-auto",[["grid-auto-columns","auto"]]),e("auto-cols-min",[["grid-auto-columns","min-content"]]),e("auto-cols-max",[["grid-auto-columns","max-content"]]),e("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),n("auto-cols",{themeKeys:["--grid-auto-columns"],handle:o=>[l("grid-auto-columns",o)]}),e("auto-rows-auto",[["grid-auto-rows","auto"]]),e("auto-rows-min",[["grid-auto-rows","min-content"]]),e("auto-rows-max",[["grid-auto-rows","max-content"]]),e("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),n("auto-rows",{themeKeys:["--grid-auto-rows"],handle:o=>[l("grid-auto-rows",o)]}),e("grid-cols-none",[["grid-template-columns","none"]]),e("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),n("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:o})=>$t(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[l("grid-template-columns",o)]}),e("grid-rows-none",[["grid-template-rows","none"]]),e("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),n("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:o})=>$t(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[l("grid-template-rows",o)]}),i("grid-cols",()=>[{values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-template-columns"]}]),i("grid-rows",()=>[{values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),e("place-content-center-safe",[["place-content","safe center"]]),e("place-content-end-safe",[["place-content","safe end"]]),e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),e("place-items-center-safe",[["place-items","safe center"]]),e("place-items-end-safe",[["place-items","safe end"]]),e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),e("content-center-safe",[["align-content","safe center"]]),e("content-end-safe",[["align-content","safe flex-end"]]),e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),e("items-center-safe",[["align-items","safe center"]]),e("items-end-safe",[["align-items","safe flex-end"]]),e("items-baseline",[["align-items","baseline"]]),e("items-baseline-last",[["align-items","last baseline"]]),e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),e("justify-center-safe",[["justify-content","safe center"]]),e("justify-end-safe",[["justify-content","safe flex-end"]]),e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),e("justify-items-center-safe",[["justify-items","safe center"]]),e("justify-items-end-safe",[["justify-items","safe end"]]),e("justify-items-stretch",[["justify-items","stretch"]]),a("gap",["--gap","--spacing"],o=>[l("gap",o)]),a("gap-x",["--gap","--spacing"],o=>[l("column-gap",o)]),a("gap-y",["--gap","--spacing"],o=>[l("row-gap",o)]),a("space-x",["--space","--spacing"],o=>[D([C("--tw-space-x-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","0"),l("margin-inline-start",`calc(${o} * var(--tw-space-x-reverse))`),l("margin-inline-end",`calc(${o} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),a("space-y",["--space","--spacing"],o=>[D([C("--tw-space-y-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","0"),l("margin-block-start",`calc(${o} * var(--tw-space-y-reverse))`),l("margin-block-end",`calc(${o} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>D([C("--tw-space-x-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>D([C("--tw-space-y-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),s("accent",{themeKeys:["--accent-color","--color"],handle:o=>[l("accent-color",o)]}),s("caret",{themeKeys:["--caret-color","--color"],handle:o=>[l("caret-color",o)]}),s("divide",{themeKeys:["--divide-color","--color"],handle:o=>[F(":where(& > :not(:last-child))",[l("--tw-sort","divide-color"),l("border-color",o)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),e("place-self-end-safe",[["place-self","safe end"]]),e("place-self-center-safe",[["place-self","safe center"]]),e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),e("self-end-safe",[["align-self","safe flex-end"]]),e("self-center-safe",[["align-self","safe center"]]),e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("self-baseline-last",[["align-self","last baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),e("justify-self-end-safe",[["justify-self","safe flex-end"]]),e("justify-self-center-safe",[["justify-self","safe center"]]),e("justify-self-stretch",[["justify-self","stretch"]]);for(let o of["auto","hidden","clip","visible","scroll"])e(`overflow-${o}`,[["overflow",o]]),e(`overflow-x-${o}`,[["overflow-x",o]]),e(`overflow-y-${o}`,[["overflow-y",o]]);for(let o of["auto","contain","none"])e(`overscroll-${o}`,[["overscroll-behavior",o]]),e(`overscroll-x-${o}`,[["overscroll-behavior-x",o]]),e(`overscroll-y-${o}`,[["overscroll-behavior-y",o]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]),e("wrap-anywhere",[["overflow-wrap","anywhere"]]),e("wrap-break-word",[["overflow-wrap","break-word"]]),e("wrap-normal",[["overflow-wrap","normal"]]);for(let[o,p]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])e(`${o}-none`,p.map(d=>[d,"0"])),e(`${o}-full`,p.map(d=>[d,"calc(infinity * 1px)"])),n(o,{themeKeys:["--radius"],handle:d=>p.map(x=>l(x,d))});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let p=function(d,x){r.functional(d,k=>{if(!k.value){if(k.modifier)return;let N=t.get(["--default-border-width"])??"1px",O=x.width(N);return O?[o(),...O]:void 0}if(k.value.kind==="arbitrary"){let N=k.value.value;switch(k.value.dataType??W(N,["color","line-width","length"])){case"line-width":case"length":{if(k.modifier)return;let $=x.width(N);return $?[o(),...$]:void 0}default:return N=Q(N,k.modifier,t),N===null?void 0:x.color(N)}}{let N=Z(k,t,["--border-color","--color"]);if(N)return x.color(N)}{if(k.modifier)return;let N=t.resolve(k.value.value,["--border-width"]);if(N){let O=x.width(N);return O?[o(),...O]:void 0}if(T(k.value.value)){let O=x.width(`${k.value.value}px`);return O?[o(),...O]:void 0}}}),i(d,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(k,N)=>`${N*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var G=p;let o=()=>D([C("--tw-border-style","solid")]);p("border",{width:d=>[l("border-style","var(--tw-border-style)"),l("border-width",d)],color:d=>[l("border-color",d)]}),p("border-x",{width:d=>[l("border-inline-style","var(--tw-border-style)"),l("border-inline-width",d)],color:d=>[l("border-inline-color",d)]}),p("border-y",{width:d=>[l("border-block-style","var(--tw-border-style)"),l("border-block-width",d)],color:d=>[l("border-block-color",d)]}),p("border-s",{width:d=>[l("border-inline-start-style","var(--tw-border-style)"),l("border-inline-start-width",d)],color:d=>[l("border-inline-start-color",d)]}),p("border-e",{width:d=>[l("border-inline-end-style","var(--tw-border-style)"),l("border-inline-end-width",d)],color:d=>[l("border-inline-end-color",d)]}),p("border-t",{width:d=>[l("border-top-style","var(--tw-border-style)"),l("border-top-width",d)],color:d=>[l("border-top-color",d)]}),p("border-r",{width:d=>[l("border-right-style","var(--tw-border-style)"),l("border-right-width",d)],color:d=>[l("border-right-color",d)]}),p("border-b",{width:d=>[l("border-bottom-style","var(--tw-border-style)"),l("border-bottom-width",d)],color:d=>[l("border-bottom-color",d)]}),p("border-l",{width:d=>[l("border-left-style","var(--tw-border-style)"),l("border-left-width",d)],color:d=>[l("border-left-color",d)]}),n("divide-x",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:d})=>T(d)?`${d}px`:null,handle:d=>[D([C("--tw-divide-x-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","divide-x-width"),o(),l("--tw-divide-x-reverse","0"),l("border-inline-style","var(--tw-border-style)"),l("border-inline-start-width",`calc(${d} * var(--tw-divide-x-reverse))`),l("border-inline-end-width",`calc(${d} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),n("divide-y",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:d})=>T(d)?`${d}px`:null,handle:d=>[D([C("--tw-divide-y-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","divide-y-width"),o(),l("--tw-divide-y-reverse","0"),l("border-bottom-style","var(--tw-border-style)"),l("border-top-style","var(--tw-border-style)"),l("border-top-width",`calc(${d} * var(--tw-divide-y-reverse))`),l("border-bottom-width",`calc(${d} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),i("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),i("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>D([C("--tw-divide-x-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>D([C("--tw-divide-y-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-divide-y-reverse","1")])]);for(let d of["solid","dashed","dotted","double","none"])e(`divide-${d}`,[()=>F(":where(& > :not(:last-child))",[l("--tw-sort","divide-style"),l("--tw-border-style",d),l("border-style",d)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),n("bg-size",{handle(o){if(o)return[l("background-size",o)]}}),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-top",[["background-position","top"]]),e("bg-top-left",[["background-position","left top"]]),e("bg-top-right",[["background-position","right top"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-bottom-left",[["background-position","left bottom"]]),e("bg-bottom-right",[["background-position","right bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-right",[["background-position","right"]]),e("bg-center",[["background-position","center"]]),n("bg-position",{handle(o){if(o)return[l("background-position",o)]}}),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let d=function(N){let O="in oklab";if(N?.kind==="named")switch(N.value){case"longer":case"shorter":case"increasing":case"decreasing":O=`in oklch ${N.value} hue`;break;default:O=`in ${N.value}`}else N?.kind==="arbitrary"&&(O=N.value);return O},x=function({negative:N}){return O=>{if(!O.value)return;if(O.value.kind==="arbitrary"){if(O.modifier)return;let U=O.value.value;switch(O.value.dataType??W(U,["angle"])){case"angle":return U=N?`calc(${U} * -1)`:`${U}`,[l("--tw-gradient-position",U),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)];default:return N?void 0:[l("--tw-gradient-position",U),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)]}}let $=O.value.value;if(!N&&p.has($))$=p.get($);else if(T($))$=N?`calc(${$}deg * -1)`:`${$}deg`;else return;let V=d(O.modifier);return[l("--tw-gradient-position",`${$}`),M("@supports (background-image: linear-gradient(in lab, red, red))",[l("--tw-gradient-position",`${$} ${V}`)]),l("background-image","linear-gradient(var(--tw-gradient-stops))")]}},k=function({negative:N}){return O=>{if(O.value?.kind==="arbitrary"){if(O.modifier)return;let U=O.value.value;return[l("--tw-gradient-position",U),l("background-image",`conic-gradient(var(--tw-gradient-stops,${U}))`)]}let $=d(O.modifier);if(!O.value)return[l("--tw-gradient-position",$),l("background-image","conic-gradient(var(--tw-gradient-stops))")];let V=O.value.value;if(T(V))return V=N?`calc(${V}deg * -1)`:`${V}deg`,[l("--tw-gradient-position",`from ${V} ${$}`),l("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var z=d,B=x,se=k;let o=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],p=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);r.functional("-bg-linear",x({negative:!0})),r.functional("bg-linear",x({negative:!1})),i("bg-linear",()=>[{values:[...p.keys()],modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("-bg-conic",k({negative:!0})),r.functional("bg-conic",k({negative:!1})),i("bg-conic",()=>[{hasDefaultValue:!0,modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("bg-radial",N=>{if(!N.value){let O=d(N.modifier);return[l("--tw-gradient-position",O),l("background-image","radial-gradient(var(--tw-gradient-stops))")]}if(N.value.kind==="arbitrary"){if(N.modifier)return;let O=N.value.value;return[l("--tw-gradient-position",O),l("background-image",`radial-gradient(var(--tw-gradient-stops,${O}))`)]}}),i("bg-radial",()=>[{hasDefaultValue:!0,modifiers:o}])}r.functional("bg",o=>{if(o.value){if(o.value.kind==="arbitrary"){let p=o.value.value;switch(o.value.dataType??W(p,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[l("background-position",p)];case"bg-size":case"length":case"size":return o.modifier?void 0:[l("background-size",p)];case"image":case"url":return o.modifier?void 0:[l("background-image",p)];default:return p=Q(p,o.modifier,t),p===null?void 0:[l("background-color",p)]}}{let p=Z(o,t,["--background-color","--color"]);if(p)return[l("background-color",p)]}{if(o.modifier)return;let p=t.resolve(o.value.value,["--background-image"]);if(p)return[l("background-image",p)]}}}),i("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let w=()=>D([C("--tw-gradient-position"),C("--tw-gradient-from","#0000","<color>"),C("--tw-gradient-via","#0000","<color>"),C("--tw-gradient-to","#0000","<color>"),C("--tw-gradient-stops"),C("--tw-gradient-via-stops"),C("--tw-gradient-from-position","0%","<length-percentage>"),C("--tw-gradient-via-position","50%","<length-percentage>"),C("--tw-gradient-to-position","100%","<length-percentage>")]);function A(o,p){r.functional(o,d=>{if(d.value){if(d.value.kind==="arbitrary"){let x=d.value.value;switch(d.value.dataType??W(x,["color","length","percentage"])){case"length":case"percentage":return d.modifier?void 0:p.position(x);default:return x=Q(x,d.modifier,t),x===null?void 0:p.color(x)}}{let x=Z(d,t,["--background-color","--color"]);if(x)return p.color(x)}{if(d.modifier)return;let x=t.resolve(d.value.value,["--gradient-color-stop-positions"]);if(x)return p.position(x);if(d.value.value[d.value.value.length-1]==="%"&&T(d.value.value.slice(0,-1)))return p.position(d.value.value)}}}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(d,x)=>`${x*5}`)},{values:Array.from({length:21},(d,x)=>`${x*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}A("from",{color:o=>[w(),l("--tw-sort","--tw-gradient-from"),l("--tw-gradient-from",o),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[w(),l("--tw-gradient-from-position",o)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),A("via",{color:o=>[w(),l("--tw-sort","--tw-gradient-via"),l("--tw-gradient-via",o),l("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),l("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:o=>[w(),l("--tw-gradient-via-position",o)]}),A("to",{color:o=>[w(),l("--tw-sort","--tw-gradient-to"),l("--tw-gradient-to",o),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[w(),l("--tw-gradient-to-position",o)]}),e("mask-none",[["mask-image","none"]]),r.functional("mask",o=>{if(!o.value||o.modifier||o.value.kind!=="arbitrary")return;let p=o.value.value;switch(o.value.dataType??W(p,["image","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[l("mask-position",p)];case"bg-size":case"length":case"size":return[l("mask-size",p)];case"image":case"url":default:return[l("mask-image",p)]}}),e("mask-add",[["mask-composite","add"]]),e("mask-subtract",[["mask-composite","subtract"]]),e("mask-intersect",[["mask-composite","intersect"]]),e("mask-exclude",[["mask-composite","exclude"]]),e("mask-alpha",[["mask-mode","alpha"]]),e("mask-luminance",[["mask-mode","luminance"]]),e("mask-match",[["mask-mode","match-source"]]),e("mask-type-alpha",[["mask-type","alpha"]]),e("mask-type-luminance",[["mask-type","luminance"]]),e("mask-auto",[["mask-size","auto"]]),e("mask-cover",[["mask-size","cover"]]),e("mask-contain",[["mask-size","contain"]]),n("mask-size",{handle(o){if(o)return[l("mask-size",o)]}}),e("mask-top",[["mask-position","top"]]),e("mask-top-left",[["mask-position","left top"]]),e("mask-top-right",[["mask-position","right top"]]),e("mask-bottom",[["mask-position","bottom"]]),e("mask-bottom-left",[["mask-position","left bottom"]]),e("mask-bottom-right",[["mask-position","right bottom"]]),e("mask-left",[["mask-position","left"]]),e("mask-right",[["mask-position","right"]]),e("mask-center",[["mask-position","center"]]),n("mask-position",{handle(o){if(o)return[l("mask-position",o)]}}),e("mask-repeat",[["mask-repeat","repeat"]]),e("mask-no-repeat",[["mask-repeat","no-repeat"]]),e("mask-repeat-x",[["mask-repeat","repeat-x"]]),e("mask-repeat-y",[["mask-repeat","repeat-y"]]),e("mask-repeat-round",[["mask-repeat","round"]]),e("mask-repeat-space",[["mask-repeat","space"]]),e("mask-clip-border",[["mask-clip","border-box"]]),e("mask-clip-padding",[["mask-clip","padding-box"]]),e("mask-clip-content",[["mask-clip","content-box"]]),e("mask-clip-fill",[["mask-clip","fill-box"]]),e("mask-clip-stroke",[["mask-clip","stroke-box"]]),e("mask-clip-view",[["mask-clip","view-box"]]),e("mask-no-clip",[["mask-clip","no-clip"]]),e("mask-origin-border",[["mask-origin","border-box"]]),e("mask-origin-padding",[["mask-origin","padding-box"]]),e("mask-origin-content",[["mask-origin","content-box"]]),e("mask-origin-fill",[["mask-origin","fill-box"]]),e("mask-origin-stroke",[["mask-origin","stroke-box"]]),e("mask-origin-view",[["mask-origin","view-box"]]);let b=()=>D([C("--tw-mask-linear","linear-gradient(#fff, #fff)"),C("--tw-mask-radial","linear-gradient(#fff, #fff)"),C("--tw-mask-conic","linear-gradient(#fff, #fff)")]);function y(o,p){r.functional(o,d=>{if(d.value){if(d.value.kind==="arbitrary"){let x=d.value.value;switch(d.value.dataType??W(x,["length","percentage","color"])){case"color":return x=Q(x,d.modifier,t),x===null?void 0:p.color(x);case"percentage":return d.modifier||!T(x.slice(0,-1))?void 0:p.position(x);default:return d.modifier?void 0:p.position(x)}}{let x=Z(d,t,["--background-color","--color"]);if(x)return p.color(x)}{if(d.modifier)return;let x=W(d.value.value,["number","percentage"]);if(!x)return;switch(x){case"number":{let k=t.resolve(null,["--spacing"]);return!k||!ke(d.value.value)?void 0:p.position(`calc(${k} * ${d.value.value})`)}case"percentage":return T(d.value.value.slice(0,-1))?p.position(d.value.value):void 0;default:return}}}}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(d,x)=>`${x*5}`)},{values:Array.from({length:21},(d,x)=>`${x*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}]),i(o,()=>[{values:Array.from({length:21},(d,x)=>`${x*5}%`)},{values:t.get(["--spacing"])?it:[]},{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(d,x)=>`${x*5}`)}])}let S=()=>D([C("--tw-mask-left","linear-gradient(#fff, #fff)"),C("--tw-mask-right","linear-gradient(#fff, #fff)"),C("--tw-mask-bottom","linear-gradient(#fff, #fff)"),C("--tw-mask-top","linear-gradient(#fff, #fff)")]);function E(o,p,d){y(o,{color(x){let k=[b(),S(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let N of["top","right","bottom","left"])d[N]&&(k.push(l(`--tw-mask-${N}`,`linear-gradient(to ${N}, var(--tw-mask-${N}-from-color) var(--tw-mask-${N}-from-position), var(--tw-mask-${N}-to-color) var(--tw-mask-${N}-to-position))`)),k.push(D([C(`--tw-mask-${N}-from-position`,"0%"),C(`--tw-mask-${N}-to-position`,"100%"),C(`--tw-mask-${N}-from-color`,"black"),C(`--tw-mask-${N}-to-color`,"transparent")])),k.push(l(`--tw-mask-${N}-${p}-color`,x)));return k},position(x){let k=[b(),S(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let N of["top","right","bottom","left"])d[N]&&(k.push(l(`--tw-mask-${N}`,`linear-gradient(to ${N}, var(--tw-mask-${N}-from-color) var(--tw-mask-${N}-from-position), var(--tw-mask-${N}-to-color) var(--tw-mask-${N}-to-position))`)),k.push(D([C(`--tw-mask-${N}-from-position`,"0%"),C(`--tw-mask-${N}-to-position`,"100%"),C(`--tw-mask-${N}-from-color`,"black"),C(`--tw-mask-${N}-to-color`,"transparent")])),k.push(l(`--tw-mask-${N}-${p}-position`,x)));return k}})}E("mask-x-from","from",{top:!1,right:!0,bottom:!1,left:!0}),E("mask-x-to","to",{top:!1,right:!0,bottom:!1,left:!0}),E("mask-y-from","from",{top:!0,right:!1,bottom:!0,left:!1}),E("mask-y-to","to",{top:!0,right:!1,bottom:!0,left:!1}),E("mask-t-from","from",{top:!0,right:!1,bottom:!1,left:!1}),E("mask-t-to","to",{top:!0,right:!1,bottom:!1,left:!1}),E("mask-r-from","from",{top:!1,right:!0,bottom:!1,left:!1}),E("mask-r-to","to",{top:!1,right:!0,bottom:!1,left:!1}),E("mask-b-from","from",{top:!1,right:!1,bottom:!0,left:!1}),E("mask-b-to","to",{top:!1,right:!1,bottom:!0,left:!1}),E("mask-l-from","from",{top:!1,right:!1,bottom:!1,left:!0}),E("mask-l-to","to",{top:!1,right:!1,bottom:!1,left:!0});let P=()=>D([C("--tw-mask-linear-position","0deg"),C("--tw-mask-linear-from-position","0%"),C("--tw-mask-linear-to-position","100%"),C("--tw-mask-linear-from-color","black"),C("--tw-mask-linear-to-color","transparent")]);n("mask-linear",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(o){return T(o.value)?`calc(1deg * ${o.value})`:null},handleNegativeBareValue(o){return T(o.value)?`calc(1deg * -${o.value})`:null},handle:o=>[b(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops, var(--tw-mask-linear-position)))"),l("--tw-mask-linear-position",o)]}),i("mask-linear",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),y("mask-linear-from",{color:o=>[b(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-from-color",o)],position:o=>[b(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-from-position",o)]}),y("mask-linear-to",{color:o=>[b(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-to-color",o)],position:o=>[b(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-to-position",o)]});let _=()=>D([C("--tw-mask-radial-from-position","0%"),C("--tw-mask-radial-to-position","100%"),C("--tw-mask-radial-from-color","black"),C("--tw-mask-radial-to-color","transparent"),C("--tw-mask-radial-shape","ellipse"),C("--tw-mask-radial-size","farthest-corner"),C("--tw-mask-radial-position","center")]);e("mask-circle",[["--tw-mask-radial-shape","circle"]]),e("mask-ellipse",[["--tw-mask-radial-shape","ellipse"]]),e("mask-radial-closest-side",[["--tw-mask-radial-size","closest-side"]]),e("mask-radial-farthest-side",[["--tw-mask-radial-size","farthest-side"]]),e("mask-radial-closest-corner",[["--tw-mask-radial-size","closest-corner"]]),e("mask-radial-farthest-corner",[["--tw-mask-radial-size","farthest-corner"]]),e("mask-radial-at-top",[["--tw-mask-radial-position","top"]]),e("mask-radial-at-top-left",[["--tw-mask-radial-position","top left"]]),e("mask-radial-at-top-right",[["--tw-mask-radial-position","top right"]]),e("mask-radial-at-bottom",[["--tw-mask-radial-position","bottom"]]),e("mask-radial-at-bottom-left",[["--tw-mask-radial-position","bottom left"]]),e("mask-radial-at-bottom-right",[["--tw-mask-radial-position","bottom right"]]),e("mask-radial-at-left",[["--tw-mask-radial-position","left"]]),e("mask-radial-at-right",[["--tw-mask-radial-position","right"]]),e("mask-radial-at-center",[["--tw-mask-radial-position","center"]]),n("mask-radial-at",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:o=>[l("--tw-mask-radial-position",o)]}),n("mask-radial",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:o=>[b(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops, var(--tw-mask-radial-size)))"),l("--tw-mask-radial-size",o)]}),y("mask-radial-from",{color:o=>[b(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-from-color",o)],position:o=>[b(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-from-position",o)]}),y("mask-radial-to",{color:o=>[b(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-to-color",o)],position:o=>[b(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-to-position",o)]});let L=()=>D([C("--tw-mask-conic-position","0deg"),C("--tw-mask-conic-from-position","0%"),C("--tw-mask-conic-to-position","100%"),C("--tw-mask-conic-from-color","black"),C("--tw-mask-conic-to-color","transparent")]);n("mask-conic",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(o){return T(o.value)?`calc(1deg * ${o.value})`:null},handleNegativeBareValue(o){return T(o.value)?`calc(1deg * -${o.value})`:null},handle:o=>[b(),L(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops, var(--tw-mask-conic-position)))"),l("--tw-mask-conic-position",o)]}),i("mask-conic",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),y("mask-conic-from",{color:o=>[b(),L(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-from-color",o)],position:o=>[b(),L(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-from-position",o)]}),y("mask-conic-to",{color:o=>[b(),L(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-to-color",o)],position:o=>[b(),L(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-to-position",o)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let o of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${o}`,[["background-blend-mode",o]]),e(`mix-blend-${o}`,[["mix-blend-mode",o]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),r.functional("fill",o=>{if(!o.value)return;if(o.value.kind==="arbitrary"){let d=Q(o.value.value,o.modifier,t);return d===null?void 0:[l("fill",d)]}let p=Z(o,t,["--fill","--color"]);if(p)return[l("fill",p)]}),i("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)}]),e("stroke-none",[["stroke","none"]]),r.functional("stroke",o=>{if(o.value){if(o.value.kind==="arbitrary"){let p=o.value.value;switch(o.value.dataType??W(p,["color","number","length","percentage"])){case"number":case"length":case"percentage":return o.modifier?void 0:[l("stroke-width",p)];default:return p=Q(o.value.value,o.modifier,t),p===null?void 0:[l("stroke",p)]}}{let p=Z(o,t,["--stroke","--color"]);if(p)return[l("stroke",p)]}{let p=t.resolve(o.value.value,["--stroke-width"]);if(p)return[l("stroke-width",p)];if(T(o.value.value))return[l("stroke-width",o.value.value)]}}}),i("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),e("object-top",[["object-position","top"]]),e("object-top-left",[["object-position","left top"]]),e("object-top-right",[["object-position","right top"]]),e("object-bottom",[["object-position","bottom"]]),e("object-bottom-left",[["object-position","left bottom"]]),e("object-bottom-right",[["object-position","right bottom"]]),e("object-left",[["object-position","left"]]),e("object-right",[["object-position","right"]]),e("object-center",[["object-position","center"]]),n("object",{themeKeys:["--object-position"],handle:o=>[l("object-position",o)]});for(let[o,p]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])a(o,["--padding","--spacing"],d=>[l(p,d)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),a("indent",["--text-indent","--spacing"],o=>[l("text-indent",o)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),n("align",{themeKeys:[],handle:o=>[l("vertical-align",o)]}),r.functional("font",o=>{if(!(!o.value||o.modifier)){if(o.value.kind==="arbitrary"){let p=o.value.value;switch(o.value.dataType??W(p,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[l("font-family",p)];default:return[D([C("--tw-font-weight")]),l("--tw-font-weight",p),l("font-weight",p)]}}{let p=t.resolveWith(o.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(p){let[d,x={}]=p;return[l("font-family",d),l("font-feature-settings",x["--font-feature-settings"]),l("font-variation-settings",x["--font-variation-settings"])]}}{let p=t.resolve(o.value.value,["--font-weight"]);if(p)return[D([C("--tw-font-weight")]),l("--tw-font-weight",p),l("font-weight",p)]}}}),i("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),n("font-stretch",{handleBareValue:({value:o})=>{if(!o.endsWith("%"))return null;let p=Number(o.slice(0,-1));return!T(p)||Number.isNaN(p)||p<50||p>200?null:o},handle:o=>[l("font-stretch",o)]}),i("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),s("placeholder",{themeKeys:["--background-color","--color"],handle:o=>[F("&::placeholder",[l("--tw-sort","placeholder-color"),l("color",o)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),r.functional("decoration",o=>{if(o.value){if(o.value.kind==="arbitrary"){let p=o.value.value;switch(o.value.dataType??W(p,["color","length","percentage"])){case"length":case"percentage":return o.modifier?void 0:[l("text-decoration-thickness",p)];default:return p=Q(p,o.modifier,t),p===null?void 0:[l("text-decoration-color",p)]}}{let p=t.resolve(o.value.value,["--text-decoration-thickness"]);if(p)return o.modifier?void 0:[l("text-decoration-thickness",p)];if(T(o.value.value))return o.modifier?void 0:[l("text-decoration-thickness",`${o.value.value}px`)]}{let p=Z(o,t,["--text-decoration-color","--color"]);if(p)return[l("text-decoration-color",p)]}}}),i("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),e("animate-none",[["animation","none"]]),n("animate",{themeKeys:["--animate"],handle:o=>[l("animation",o)]});{let o=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),p=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),d=()=>D([C("--tw-blur"),C("--tw-brightness"),C("--tw-contrast"),C("--tw-grayscale"),C("--tw-hue-rotate"),C("--tw-invert"),C("--tw-opacity"),C("--tw-saturate"),C("--tw-sepia"),C("--tw-drop-shadow"),C("--tw-drop-shadow-color"),C("--tw-drop-shadow-alpha","100%","<percentage>"),C("--tw-drop-shadow-size")]),x=()=>D([C("--tw-backdrop-blur"),C("--tw-backdrop-brightness"),C("--tw-backdrop-contrast"),C("--tw-backdrop-grayscale"),C("--tw-backdrop-hue-rotate"),C("--tw-backdrop-invert"),C("--tw-backdrop-opacity"),C("--tw-backdrop-saturate"),C("--tw-backdrop-sepia")]);r.functional("filter",k=>{if(!k.modifier){if(k.value===null)return[d(),l("filter",o)];if(k.value.kind==="arbitrary")return[l("filter",k.value.value)];switch(k.value.value){case"none":return[l("filter","none")]}}}),r.functional("backdrop-filter",k=>{if(!k.modifier){if(k.value===null)return[x(),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)];if(k.value.kind==="arbitrary")return[l("-webkit-backdrop-filter",k.value.value),l("backdrop-filter",k.value.value)];switch(k.value.value){case"none":return[l("-webkit-backdrop-filter","none"),l("backdrop-filter","none")]}}}),n("blur",{themeKeys:["--blur"],handle:k=>[d(),l("--tw-blur",`blur(${k})`),l("filter",o)]}),e("blur-none",[d,["--tw-blur"," "],["filter",o]]),n("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:k=>[x(),l("--tw-backdrop-blur",`blur(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),e("backdrop-blur-none",[x,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",p],["backdrop-filter",p]]),n("brightness",{themeKeys:["--brightness"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[d(),l("--tw-brightness",`brightness(${k})`),l("filter",o)]}),n("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[x(),l("--tw-backdrop-brightness",`brightness(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),i("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),n("contrast",{themeKeys:["--contrast"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[d(),l("--tw-contrast",`contrast(${k})`),l("filter",o)]}),n("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[x(),l("--tw-backdrop-contrast",`contrast(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),i("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),n("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[d(),l("--tw-grayscale",`grayscale(${k})`),l("filter",o)]}),n("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[x(),l("--tw-backdrop-grayscale",`grayscale(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),i("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),n("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:k})=>T(k)?`${k}deg`:null,handle:k=>[d(),l("--tw-hue-rotate",`hue-rotate(${k})`),l("filter",o)]}),n("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:k})=>T(k)?`${k}deg`:null,handle:k=>[x(),l("--tw-backdrop-hue-rotate",`hue-rotate(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),i("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),n("invert",{themeKeys:["--invert"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[d(),l("--tw-invert",`invert(${k})`),l("filter",o)]}),n("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[x(),l("--tw-backdrop-invert",`invert(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),i("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),n("saturate",{themeKeys:["--saturate"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[d(),l("--tw-saturate",`saturate(${k})`),l("filter",o)]}),n("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[x(),l("--tw-backdrop-saturate",`saturate(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),i("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),n("sepia",{themeKeys:["--sepia"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[d(),l("--tw-sepia",`sepia(${k})`),l("filter",o)]}),n("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[x(),l("--tw-backdrop-sepia",`sepia(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),i("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[d,["--tw-drop-shadow"," "],["filter",o]]),r.functional("drop-shadow",k=>{let N;if(k.modifier&&(k.modifier.kind==="arbitrary"?N=k.modifier.value:T(k.modifier.value)&&(N=`${k.modifier.value}%`)),!k.value){let O=t.get(["--drop-shadow"]),$=t.resolve(null,["--drop-shadow"]);return O===null||$===null?void 0:[d(),l("--tw-drop-shadow-alpha",N),...rt("--tw-drop-shadow-size",O,N,V=>`var(--tw-drop-shadow-color, ${V})`),l("--tw-drop-shadow",K($,",").map(V=>`drop-shadow(${V})`).join(" ")),l("filter",o)]}if(k.value.kind==="arbitrary"){let O=k.value.value;switch(k.value.dataType??W(O,["color"])){case"color":return O=Q(O,k.modifier,t),O===null?void 0:[d(),l("--tw-drop-shadow-color",Y(O,"var(--tw-drop-shadow-alpha)")),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")];default:return k.modifier&&!N?void 0:[d(),l("--tw-drop-shadow-alpha",N),...rt("--tw-drop-shadow-size",O,N,V=>`var(--tw-drop-shadow-color, ${V})`),l("--tw-drop-shadow","var(--tw-drop-shadow-size)"),l("filter",o)]}}{let O=t.get([`--drop-shadow-${k.value.value}`]),$=t.resolve(k.value.value,["--drop-shadow"]);if(O&&$)return k.modifier&&!N?void 0:N?[d(),l("--tw-drop-shadow-alpha",N),...rt("--tw-drop-shadow-size",O,N,V=>`var(--tw-drop-shadow-color, ${V})`),l("--tw-drop-shadow","var(--tw-drop-shadow-size)"),l("filter",o)]:[d(),l("--tw-drop-shadow-alpha",N),...rt("--tw-drop-shadow-size",O,N,V=>`var(--tw-drop-shadow-color, ${V})`),l("--tw-drop-shadow",K($,",").map(V=>`drop-shadow(${V})`).join(" ")),l("filter",o)]}{let O=Z(k,t,["--drop-shadow-color","--color"]);if(O)return[d(),l("--tw-drop-shadow-color",Y(O,"var(--tw-drop-shadow-alpha)")),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")]}}),i("drop-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--drop-shadow-color","--color"],modifiers:Array.from({length:21},(k,N)=>`${N*5}`)},{valueThemeKeys:["--drop-shadow"]}]),n("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:k})=>tt(k)?`${k}%`:null,handle:k=>[x(),l("--tw-backdrop-opacity",`opacity(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("backdrop-opacity",()=>[{values:Array.from({length:21},(k,N)=>`${N*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let o=`var(--tw-ease, ${t.resolve(null,["--default-transition-timing-function"])??"ease"})`,p=`var(--tw-duration, ${t.resolve(null,["--default-transition-duration"])??"0s"})`;e("transition-none",[["transition-property","none"]]),e("transition-all",[["transition-property","all"],["transition-timing-function",o],["transition-duration",p]]),e("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",o],["transition-duration",p]]),e("transition-opacity",[["transition-property","opacity"],["transition-timing-function",o],["transition-duration",p]]),e("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",o],["transition-duration",p]]),e("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",o],["transition-duration",p]]),n("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter",themeKeys:["--transition-property"],handle:d=>[l("transition-property",d),l("transition-timing-function",o),l("transition-duration",p)]}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),n("delay",{handleBareValue:({value:d})=>T(d)?`${d}ms`:null,themeKeys:["--transition-delay"],handle:d=>[l("transition-delay",d)]});{let d=()=>D([C("--tw-duration")]);e("duration-initial",[d,["--tw-duration","initial"]]),r.functional("duration",x=>{if(x.modifier||!x.value)return;let k=null;if(x.value.kind==="arbitrary"?k=x.value.value:(k=t.resolve(x.value.fraction??x.value.value,["--transition-duration"]),k===null&&T(x.value.value)&&(k=`${x.value.value}ms`)),k!==null)return[d(),l("--tw-duration",k),l("transition-duration",k)]})}i("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),i("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let o=()=>D([C("--tw-ease")]);e("ease-initial",[o,["--tw-ease","initial"]]),e("ease-linear",[o,["--tw-ease","linear"],["transition-timing-function","linear"]]),n("ease",{themeKeys:["--ease"],handle:p=>[o(),l("--tw-ease",p),l("transition-timing-function",p)]})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),n("will-change",{themeKeys:[],handle:o=>[l("will-change",o)]}),e("content-none",[["--tw-content","none"],["content","none"]]),n("content",{themeKeys:[],handle:o=>[D([C("--tw-content",'""')]),l("--tw-content",o),l("content","var(--tw-content)")]});{let o="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",p=()=>D([C("--tw-contain-size"),C("--tw-contain-layout"),C("--tw-contain-paint"),C("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[p,["--tw-contain-size","size"],["contain",o]]),e("contain-inline-size",[p,["--tw-contain-size","inline-size"],["contain",o]]),e("contain-layout",[p,["--tw-contain-layout","layout"],["contain",o]]),e("contain-paint",[p,["--tw-contain-paint","paint"],["contain",o]]),e("contain-style",[p,["--tw-contain-style","style"],["contain",o]]),n("contain",{themeKeys:[],handle:d=>[l("contain",d)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),e("leading-none",[()=>D([C("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),a("leading",["--leading","--spacing"],o=>[D([C("--tw-leading")]),l("--tw-leading",o),l("line-height",o)]),n("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:o=>[D([C("--tw-tracking")]),l("--tw-tracking",o),l("letter-spacing",o)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let o="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",p=()=>D([C("--tw-ordinal"),C("--tw-slashed-zero"),C("--tw-numeric-figure"),C("--tw-numeric-spacing"),C("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[p,["--tw-ordinal","ordinal"],["font-variant-numeric",o]]),e("slashed-zero",[p,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",o]]),e("lining-nums",[p,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",o]]),e("oldstyle-nums",[p,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",o]]),e("proportional-nums",[p,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",o]]),e("tabular-nums",[p,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",o]]),e("diagonal-fractions",[p,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",o]]),e("stacked-fractions",[p,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",o]])}{let o=()=>D([C("--tw-outline-style","solid")]);r.static("outline-hidden",()=>[l("--tw-outline-style","none"),l("outline-style","none"),j("@media","(forced-colors: active)",[l("outline","2px solid transparent"),l("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),r.functional("outline",p=>{if(p.value===null){if(p.modifier)return;let d=t.get(["--default-outline-width"])??"1px";return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",d)]}if(p.value.kind==="arbitrary"){let d=p.value.value;switch(p.value.dataType??W(d,["color","length","number","percentage"])){case"length":case"number":case"percentage":return p.modifier?void 0:[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",d)];default:return d=Q(d,p.modifier,t),d===null?void 0:[l("outline-color",d)]}}{let d=Z(p,t,["--outline-color","--color"]);if(d)return[l("outline-color",d)]}{if(p.modifier)return;let d=t.resolve(p.value.value,["--outline-width"]);if(d)return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",d)];if(T(p.value.value))return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",`${p.value.value}px`)]}}),i("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(p,d)=>`${d*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),n("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:p})=>T(p)?`${p}px`:null,handle:p=>[l("outline-offset",p)]}),i("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}n("opacity",{themeKeys:["--opacity"],handleBareValue:({value:o})=>tt(o)?`${o}%`:null,handle:o=>[l("opacity",o)]}),i("opacity",()=>[{values:Array.from({length:21},(o,p)=>`${p*5}`),valueThemeKeys:["--opacity"]}]),e("underline-offset-auto",[["text-underline-offset","auto"]]),n("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:o})=>T(o)?`${o}px`:null,handle:o=>[l("text-underline-offset",o)]}),i("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),r.functional("text",o=>{if(o.value){if(o.value.kind==="arbitrary"){let p=o.value.value;switch(o.value.dataType??W(p,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(o.modifier){let x=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!x&&ke(o.modifier.value)){let k=t.resolve(null,["--spacing"]);if(!k)return null;x=`calc(${k} * ${o.modifier.value})`}return!x&&o.modifier.value==="none"&&(x="1"),x?[l("font-size",p),l("line-height",x)]:null}return[l("font-size",p)]}default:return p=Q(p,o.modifier,t),p===null?void 0:[l("color",p)]}}{let p=Z(o,t,["--text-color","--color"]);if(p)return[l("color",p)]}{let p=t.resolveWith(o.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(p){let[d,x={}]=Array.isArray(p)?p:[p];if(o.modifier){let k=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!k&&ke(o.modifier.value)){let O=t.resolve(null,["--spacing"]);if(!O)return null;k=`calc(${O} * ${o.modifier.value})`}if(!k&&o.modifier.value==="none"&&(k="1"),!k)return null;let N=[l("font-size",d)];return k&&N.push(l("line-height",k)),N}return typeof x=="string"?[l("font-size",d),l("line-height",x)]:[l("font-size",d),l("line-height",x["--line-height"]?`var(--tw-leading, ${x["--line-height"]})`:void 0),l("letter-spacing",x["--letter-spacing"]?`var(--tw-tracking, ${x["--letter-spacing"]})`:void 0),l("font-weight",x["--font-weight"]?`var(--tw-font-weight, ${x["--font-weight"]})`:void 0)]}}}}),i("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);let R=()=>D([C("--tw-text-shadow-color"),C("--tw-text-shadow-alpha","100%","<percentage>")]);e("text-shadow-initial",[R,["--tw-text-shadow-color","initial"]]),r.functional("text-shadow",o=>{let p;if(o.modifier&&(o.modifier.kind==="arbitrary"?p=o.modifier.value:T(o.modifier.value)&&(p=`${o.modifier.value}%`)),!o.value){let d=t.get(["--text-shadow"]);return d===null?void 0:[R(),l("--tw-text-shadow-alpha",p),...ae("text-shadow",d,p,x=>`var(--tw-text-shadow-color, ${x})`)]}if(o.value.kind==="arbitrary"){let d=o.value.value;switch(o.value.dataType??W(d,["color"])){case"color":return d=Q(d,o.modifier,t),d===null?void 0:[R(),l("--tw-text-shadow-color",Y(d,"var(--tw-text-shadow-alpha)"))];default:return[R(),l("--tw-text-shadow-alpha",p),...ae("text-shadow",d,p,k=>`var(--tw-text-shadow-color, ${k})`)]}}switch(o.value.value){case"none":return o.modifier?void 0:[R(),l("text-shadow","none")]}{let d=t.get([`--text-shadow-${o.value.value}`]);if(d)return[R(),l("--tw-text-shadow-alpha",p),...ae("text-shadow",d,p,x=>`var(--tw-text-shadow-color, ${x})`)]}{let d=Z(o,t,["--text-shadow-color","--color"]);if(d)return[R(),l("--tw-text-shadow-color",Y(d,"var(--tw-text-shadow-alpha)"))]}}),i("text-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-shadow-color","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:["none"]},{valueThemeKeys:["--text-shadow"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`),hasDefaultValue:!0}]);{let k=function($){return`var(--tw-ring-inset,) 0 0 0 calc(${$} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${x})`},N=function($){return`inset 0 0 0 ${$} var(--tw-inset-ring-color, currentcolor)`};var xe=k,Dt=N;let o=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),p="0 0 #0000",d=()=>D([C("--tw-shadow",p),C("--tw-shadow-color"),C("--tw-shadow-alpha","100%","<percentage>"),C("--tw-inset-shadow",p),C("--tw-inset-shadow-color"),C("--tw-inset-shadow-alpha","100%","<percentage>"),C("--tw-ring-color"),C("--tw-ring-shadow",p),C("--tw-inset-ring-color"),C("--tw-inset-ring-shadow",p),C("--tw-ring-inset"),C("--tw-ring-offset-width","0px","<length>"),C("--tw-ring-offset-color","#fff"),C("--tw-ring-offset-shadow",p)]);e("shadow-initial",[d,["--tw-shadow-color","initial"]]),r.functional("shadow",$=>{let V;if($.modifier&&($.modifier.kind==="arbitrary"?V=$.modifier.value:T($.modifier.value)&&(V=`${$.modifier.value}%`)),!$.value){let U=t.get(["--shadow"]);return U===null?void 0:[d(),l("--tw-shadow-alpha",V),...ae("--tw-shadow",U,V,ie=>`var(--tw-shadow-color, ${ie})`),l("box-shadow",o)]}if($.value.kind==="arbitrary"){let U=$.value.value;switch($.value.dataType??W(U,["color"])){case"color":return U=Q(U,$.modifier,t),U===null?void 0:[d(),l("--tw-shadow-color",Y(U,"var(--tw-shadow-alpha)"))];default:return[d(),l("--tw-shadow-alpha",V),...ae("--tw-shadow",U,V,mt=>`var(--tw-shadow-color, ${mt})`),l("box-shadow",o)]}}switch($.value.value){case"none":return $.modifier?void 0:[d(),l("--tw-shadow",p),l("box-shadow",o)]}{let U=t.get([`--shadow-${$.value.value}`]);if(U)return[d(),l("--tw-shadow-alpha",V),...ae("--tw-shadow",U,V,ie=>`var(--tw-shadow-color, ${ie})`),l("box-shadow",o)]}{let U=Z($,t,["--box-shadow-color","--color"]);if(U)return[d(),l("--tw-shadow-color",Y(U,"var(--tw-shadow-alpha)"))]}}),i("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},($,V)=>`${V*5}`)},{values:["none"]},{valueThemeKeys:["--shadow"],modifiers:Array.from({length:21},($,V)=>`${V*5}`),hasDefaultValue:!0}]),e("inset-shadow-initial",[d,["--tw-inset-shadow-color","initial"]]),r.functional("inset-shadow",$=>{let V;if($.modifier&&($.modifier.kind==="arbitrary"?V=$.modifier.value:T($.modifier.value)&&(V=`${$.modifier.value}%`)),!$.value){let U=t.get(["--inset-shadow"]);return U===null?void 0:[d(),l("--tw-inset-shadow-alpha",V),...ae("--tw-inset-shadow",U,V,ie=>`var(--tw-inset-shadow-color, ${ie})`),l("box-shadow",o)]}if($.value.kind==="arbitrary"){let U=$.value.value;switch($.value.dataType??W(U,["color"])){case"color":return U=Q(U,$.modifier,t),U===null?void 0:[d(),l("--tw-inset-shadow-color",Y(U,"var(--tw-inset-shadow-alpha)"))];default:return[d(),l("--tw-inset-shadow-alpha",V),...ae("--tw-inset-shadow",U,V,mt=>`var(--tw-inset-shadow-color, ${mt})`,"inset "),l("box-shadow",o)]}}switch($.value.value){case"none":return $.modifier?void 0:[d(),l("--tw-inset-shadow",p),l("box-shadow",o)]}{let U=t.get([`--inset-shadow-${$.value.value}`]);if(U)return[d(),l("--tw-inset-shadow-alpha",V),...ae("--tw-inset-shadow",U,V,ie=>`var(--tw-inset-shadow-color, ${ie})`),l("box-shadow",o)]}{let U=Z($,t,["--box-shadow-color","--color"]);if(U)return[d(),l("--tw-inset-shadow-color",Y(U,"var(--tw-inset-shadow-alpha)"))]}}),i("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},($,V)=>`${V*5}`)},{values:["none"]},{valueThemeKeys:["--inset-shadow"],modifiers:Array.from({length:21},($,V)=>`${V*5}`),hasDefaultValue:!0}]),e("ring-inset",[d,["--tw-ring-inset","inset"]]);let x=t.get(["--default-ring-color"])??"currentcolor";r.functional("ring",$=>{if(!$.value){if($.modifier)return;let V=t.get(["--default-ring-width"])??"1px";return[d(),l("--tw-ring-shadow",k(V)),l("box-shadow",o)]}if($.value.kind==="arbitrary"){let V=$.value.value;switch($.value.dataType??W(V,["color","length"])){case"length":return $.modifier?void 0:[d(),l("--tw-ring-shadow",k(V)),l("box-shadow",o)];default:return V=Q(V,$.modifier,t),V===null?void 0:[l("--tw-ring-color",V)]}}{let V=Z($,t,["--ring-color","--color"]);if(V)return[l("--tw-ring-color",V)]}{if($.modifier)return;let V=t.resolve($.value.value,["--ring-width"]);if(V===null&&T($.value.value)&&(V=`${$.value.value}px`),V)return[d(),l("--tw-ring-shadow",k(V)),l("box-shadow",o)]}}),i("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},($,V)=>`${V*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),r.functional("inset-ring",$=>{if(!$.value)return $.modifier?void 0:[d(),l("--tw-inset-ring-shadow",N("1px")),l("box-shadow",o)];if($.value.kind==="arbitrary"){let V=$.value.value;switch($.value.dataType??W(V,["color","length"])){case"length":return $.modifier?void 0:[d(),l("--tw-inset-ring-shadow",N(V)),l("box-shadow",o)];default:return V=Q(V,$.modifier,t),V===null?void 0:[l("--tw-inset-ring-color",V)]}}{let V=Z($,t,["--ring-color","--color"]);if(V)return[l("--tw-inset-ring-color",V)]}{if($.modifier)return;let V=t.resolve($.value.value,["--ring-width"]);if(V===null&&T($.value.value)&&(V=`${$.value.value}px`),V)return[d(),l("--tw-inset-ring-shadow",N(V)),l("box-shadow",o)]}}),i("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},($,V)=>`${V*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let O="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";r.functional("ring-offset",$=>{if($.value){if($.value.kind==="arbitrary"){let V=$.value.value;switch($.value.dataType??W(V,["color","length"])){case"length":return $.modifier?void 0:[l("--tw-ring-offset-width",V),l("--tw-ring-offset-shadow",O)];default:return V=Q(V,$.modifier,t),V===null?void 0:[l("--tw-ring-offset-color",V)]}}{let V=t.resolve($.value.value,["--ring-offset-width"]);if(V)return $.modifier?void 0:[l("--tw-ring-offset-width",V),l("--tw-ring-offset-shadow",O)];if(T($.value.value))return $.modifier?void 0:[l("--tw-ring-offset-width",`${$.value.value}px`),l("--tw-ring-offset-shadow",O)]}{let V=Z($,t,["--ring-offset-color","--color"]);if(V)return[l("--tw-ring-offset-color",V)]}}})}return i("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),r.functional("@container",o=>{let p=null;if(o.value===null?p="inline-size":o.value.kind==="arbitrary"?p=o.value.value:o.value.kind==="named"&&o.value.value==="normal"&&(p="normal"),p!==null)return o.modifier?[l("container-type",p),l("container-name",o.modifier.value)]:[l("container-type",p)]}),i("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),r}var St=["number","integer","ratio","percentage"];function wr(t){let r=t.params;return nn.test(r)?i=>{let e={"--value":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set},"--modifier":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set}};I(t.nodes,n=>{if(n.kind!=="declaration"||!n.value||!n.value.includes("--value(")&&!n.value.includes("--modifier("))return;let s=H(n.value);ee(s,a=>{if(a.kind!=="function")return;if(a.value==="--spacing"&&!(e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return ee(a.nodes,u=>{if(u.kind!=="function"||u.value!=="--value"&&u.value!=="--modifier")return;let c=u.value;for(let g of u.nodes)if(g.kind==="word"){if(g.value==="integer")e[c].usedSpacingInteger||=!0;else if(g.value==="number"&&(e[c].usedSpacingNumber||=!0,e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return 2}}),0;if(a.value!=="--value"&&a.value!=="--modifier")return;let f=K(J(a.nodes),",");for(let[u,c]of f.entries())c=c.replace(/\\\*/g,"*"),c=c.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),c=c.replace(/\s+/g,""),c=c.replace(/(-\*){2,}/g,"-*"),c[0]==="-"&&c[1]==="-"&&!c.includes("-*")&&(c+="-*"),f[u]=c;a.nodes=H(f.join(","));for(let u of a.nodes)if(u.kind==="word"&&(u.value[0]==='"'||u.value[0]==="'")&&u.value[0]===u.value[u.value.length-1]){let c=u.value.slice(1,-1);e[a.value].literals.add(c)}else if(u.kind==="word"&&u.value[0]==="-"&&u.value[1]==="-"){let c=u.value.replace(/-\*.*$/g,"");e[a.value].themeKeys.add(c)}else if(u.kind==="word"&&!(u.value[0]==="["&&u.value[u.value.length-1]==="]")&&!St.includes(u.value)){console.warn(`Unsupported bare value data type: "${u.value}".
Only valid data types are: ${St.map(A=>`"${A}"`).join(", ")}.
`);let c=u.value,g=structuredClone(a),m="\xB6";ee(g.nodes,(A,{replaceWith:b})=>{A.kind==="word"&&A.value===c&&b({kind:"word",value:m})});let h="^".repeat(J([u]).length),v=J([g]).indexOf(m),w=["```css",J([a])," ".repeat(v)+h,"```"].join(`
`);console.warn(w)}}),n.value=J(s)}),i.utilities.functional(r.slice(0,-2),n=>{let s=structuredClone(t),a=n.value,f=n.modifier;if(a===null)return;let u=!1,c=!1,g=!1,m=!1,h=new Map,v=!1;if(I([s],(w,{parent:A,replaceWith:b})=>{if(A?.kind!=="rule"&&A?.kind!=="at-rule"||w.kind!=="declaration"||!w.value)return;let y=H(w.value);(ee(y,(E,{replaceWith:P})=>{if(E.kind==="function"){if(E.value==="--value"){u=!0;let _=gr(a,E,i);return _?(c=!0,_.ratio?v=!0:h.set(w,A),P(_.nodes),1):(u||=!1,b([]),2)}else if(E.value==="--modifier"){if(f===null)return b([]),2;g=!0;let _=gr(f,E,i);return _?(m=!0,P(_.nodes),1):(g||=!1,b([]),2)}}})??0)===0&&(w.value=J(y))}),u&&!c||g&&!m||v&&m||f&&!v&&!m)return null;if(v)for(let[w,A]of h){let b=A.nodes.indexOf(w);b!==-1&&A.nodes.splice(b,1)}return s.nodes}),i.utilities.suggest(r.slice(0,-2),()=>{let n=[],s=[];for(let[a,{literals:f,usedSpacingNumber:u,usedSpacingInteger:c,themeKeys:g}]of[[n,e["--value"]],[s,e["--modifier"]]]){for(let m of f)a.push(m);if(u)a.push(...it);else if(c)for(let m of it)T(m)&&a.push(m);for(let m of i.theme.keysInNamespaces(g))a.push(m)}return[{values:n,modifiers:s}]})}:rn.test(r)?i=>{i.utilities.static(r,()=>structuredClone(t.nodes))}:null}function gr(t,r,i){for(let e of r.nodes){if(t.kind==="named"&&e.kind==="word"&&(e.value[0]==="'"||e.value[0]==='"')&&e.value[e.value.length-1]===e.value[0]&&e.value.slice(1,-1)===t.value)return{nodes:H(t.value)};if(t.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let n=e.value;if(n.endsWith("-*")){n=n.slice(0,-2);let s=i.theme.resolve(t.value,[n]);if(s)return{nodes:H(s)}}else{let s=n.split("-*");if(s.length<=1)continue;let a=[s.shift()],f=i.theme.resolveWith(t.value,a,s);if(f){let[,u={}]=f;{let c=u[s.pop()];if(c)return{nodes:H(c)}}}}}else if(t.kind==="named"&&e.kind==="word"){if(!St.includes(e.value))continue;let n=e.value==="ratio"&&"fraction"in t?t.fraction:t.value;if(!n)continue;let s=W(n,[e.value]);if(s===null)continue;if(s==="ratio"){let[a,f]=K(n,"/");if(!T(a)||!T(f))continue}else{if(s==="number"&&!ke(n))continue;if(s==="percentage"&&!T(n.slice(0,-1)))continue}return{nodes:H(n),ratio:s==="ratio"}}else if(t.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let n=e.value.slice(1,-1);if(n==="*")return{nodes:H(t.value)};if("dataType"in t&&t.dataType&&t.dataType!==n)continue;if("dataType"in t&&t.dataType)return{nodes:H(t.value)};if(W(t.value,[n])!==null)return{nodes:H(t.value)}}}}function ae(t,r,i,e,n=""){let s=!1,a=_e(r,u=>i==null?e(u):u.startsWith("current")?e(Y(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(hr(u,i))));function f(u){return n?K(u,",").map(c=>n+c).join(","):u}return s?[l(t,f(_e(r,e))),M("@supports (color: lab(from red l a b))",[l(t,f(a))])]:[l(t,f(a))]}function rt(t,r,i,e,n=""){let s=!1,a=K(r,",").map(f=>_e(f,u=>i==null?e(u):u.startsWith("current")?e(Y(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(hr(u,i))))).map(f=>`drop-shadow(${f})`).join(" ");return s?[l(t,n+K(r,",").map(f=>`drop-shadow(${_e(f,e)})`).join(" ")),M("@supports (color: lab(from red l a b))",[l(t,n+a)])]:[l(t,n+a)]}var Tt={"--alpha":on,"--spacing":ln,"--theme":an,theme:sn};function on(t,r,i,...e){let[n,s]=K(i,"/").map(a=>a.trim());if(!n||!s)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${n||"var(--my-color)"} / ${s||"50%"})\``);if(e.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${n||"var(--my-color)"} / ${s||"50%"})\``);return Y(n,s)}function ln(t,r,i,...e){if(!i)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(e.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${e.length+1}.`);let n=t.theme.resolve(null,["--spacing"]);if(!n)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${n} * ${i})`}function an(t,r,i,...e){if(!i.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let n=!1;i.endsWith(" inline")&&(n=!0,i=i.slice(0,-7)),r.kind==="at-rule"&&(n=!0);let s=t.resolveThemeValue(i,n);if(!s){if(e.length>0)return e.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(e.length===0)return s;let a=e.join(", ");if(a==="initial")return s;if(s==="initial")return a;if(s.startsWith("var(")||s.startsWith("theme(")||s.startsWith("--theme(")){let f=H(s);return cn(f,a),J(f)}return s}function sn(t,r,i,...e){i=un(i);let n=t.resolveThemeValue(i);if(!n&&e.length>0)return e.join(", ");if(!n)throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return n}var kr=new RegExp(Object.keys(Tt).map(t=>`${t}\\(`).join("|"));function Ne(t,r){let i=0;return I(t,e=>{if(e.kind==="declaration"&&e.value&&kr.test(e.value)){i|=8,e.value=yr(e.value,e,r);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&kr.test(e.params)&&(i|=8,e.params=yr(e.params,e,r))}),i}function yr(t,r,i){let e=H(t);return ee(e,(n,{replaceWith:s})=>{if(n.kind==="function"&&n.value in Tt){let a=K(J(n.nodes).trim(),",").map(u=>u.trim()),f=Tt[n.value](i,r,...a);return s(H(f))}}),J(e)}function un(t){if(t[0]!=="'"&&t[0]!=='"')return t;let r="",i=t[0];for(let e=1;e<t.length-1;e++){let n=t[e],s=t[e+1];n==="\\"&&(s===i||s==="\\")?(r+=s,e++):r+=n}return r}function cn(t,r){ee(t,i=>{if(i.kind==="function"&&!(i.value!=="var"&&i.value!=="theme"&&i.value!=="--theme"))if(i.nodes.length===1)i.nodes.push({kind:"word",value:`, ${r}`});else{let e=i.nodes[i.nodes.length-1];e.kind==="word"&&e.value==="initial"&&(e.value=r)}})}function nt(t,r){let i=t.length,e=r.length,n=i<e?i:e;for(let s=0;s<n;s++){let a=t.charCodeAt(s),f=r.charCodeAt(s);if(a>=48&&a<=57&&f>=48&&f<=57){let u=s,c=s+1,g=s,m=s+1;for(a=t.charCodeAt(c);a>=48&&a<=57;)a=t.charCodeAt(++c);for(f=r.charCodeAt(m);f>=48&&f<=57;)f=r.charCodeAt(++m);let h=t.slice(u,c),v=r.slice(g,m),w=Number(h)-Number(v);if(w)return w;if(h<v)return-1;if(h>v)return 1;continue}if(a!==f)return a-f}return t.length-r.length}var fn=/^\d+\/\d+$/;function br(t){let r=[];for(let e of t.utilities.keys("static"))r.push({name:e,utility:e,fraction:!1,modifiers:[]});for(let e of t.utilities.keys("functional")){let n=t.utilities.getCompletions(e);for(let s of n)for(let a of s.values){let f=a!==null&&fn.test(a),u=a===null?e:`${e}-${a}`;r.push({name:u,utility:e,fraction:f,modifiers:s.modifiers}),s.supportsNegative&&r.push({name:`-${u}`,utility:`-${e}`,fraction:f,modifiers:s.modifiers})}}return r.length===0?[]:(r.sort((e,n)=>nt(e.name,n.name)),pn(r))}function pn(t){let r=[],i=null,e=new Map,n=new q(()=>[]);for(let a of t){let{utility:f,fraction:u}=a;i||(i={utility:f,items:[]},e.set(f,i)),f!==i.utility&&(r.push(i),i={utility:f,items:[]},e.set(f,i)),u?n.get(f).push(a):i.items.push(a)}i&&r[r.length-1]!==i&&r.push(i);for(let[a,f]of n){let u=e.get(a);u&&u.items.push(...f)}let s=[];for(let a of r)for(let f of a.items)s.push([f.name,{modifiers:f.modifiers}]);return s}function xr(t){let r=[];for(let[e,n]of t.variants.entries()){let f=function({value:u,modifier:c}={}){let g=e;u&&(g+=s?`-${u}`:u),c&&(g+=`/${c}`);let m=t.parseVariant(g);if(!m)return[];let h=F(".__placeholder__",[]);if(Ve(h,m,t.variants)===null)return[];let v=[];return Ye(h.nodes,(w,{path:A})=>{if(w.kind!=="rule"&&w.kind!=="at-rule"||w.nodes.length>0)return;A.sort((S,E)=>{let P=S.kind==="at-rule",_=E.kind==="at-rule";return P&&!_?-1:!P&&_?1:0});let b=A.flatMap(S=>S.kind==="rule"?S.selector==="&"?[]:[S.selector]:S.kind==="at-rule"?[`${S.name} ${S.params}`]:[]),y="";for(let S=b.length-1;S>=0;S--)y=y===""?b[S]:`${b[S]} { ${y} }`;v.push(y)}),v};var i=f;if(n.kind==="arbitrary")continue;let s=e!=="@",a=t.variants.getCompletions(e);switch(n.kind){case"static":{r.push({name:e,values:a,isArbitrary:!1,hasDash:s,selectors:f});break}case"functional":{r.push({name:e,values:a,isArbitrary:!0,hasDash:s,selectors:f});break}case"compound":{r.push({name:e,values:a,isArbitrary:!0,hasDash:s,selectors:f});break}}}return r}function Ar(t,r){let{astNodes:i,nodeSorting:e}=pe(Array.from(r),t),n=new Map(r.map(a=>[a,null])),s=0n;for(let a of i){let f=e.get(a)?.candidate;f&&n.set(f,n.get(f)??s++)}return r.map(a=>[a,n.get(a)??null])}var ot=/^@?[a-zA-Z0-9_-]*$/;var Et=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(r,i,{compounds:e,order:n}={}){this.set(r,{kind:"static",applyFn:i,compoundsWith:0,compounds:e??2,order:n})}fromAst(r,i){let e=[];I(i,n=>{n.kind==="rule"?e.push(n.selector):n.kind==="at-rule"&&n.name!=="@slot"&&e.push(`${n.name} ${n.params}`)}),this.static(r,n=>{let s=structuredClone(i);Rt(s,n.nodes),n.nodes=s},{compounds:ye(e)})}functional(r,i,{compounds:e,order:n}={}){this.set(r,{kind:"functional",applyFn:i,compoundsWith:0,compounds:e??2,order:n})}compound(r,i,e,{compounds:n,order:s}={}){this.set(r,{kind:"compound",applyFn:e,compoundsWith:i,compounds:n??2,order:s})}group(r,i){this.groupOrder=this.nextOrder(),i&&this.compareFns.set(this.groupOrder,i),r(),this.groupOrder=null}has(r){return this.variants.has(r)}get(r){return this.variants.get(r)}kind(r){return this.variants.get(r)?.kind}compoundsWith(r,i){let e=this.variants.get(r),n=typeof i=="string"?this.variants.get(i):i.kind==="arbitrary"?{compounds:ye([i.selector])}:this.variants.get(i.root);return!(!e||!n||e.kind!=="compound"||n.compounds===0||e.compoundsWith===0||(e.compoundsWith&n.compounds)===0)}suggest(r,i){this.completions.set(r,i)}getCompletions(r){return this.completions.get(r)?.()??[]}compare(r,i){if(r===i)return 0;if(r===null)return-1;if(i===null)return 1;if(r.kind==="arbitrary"&&i.kind==="arbitrary")return r.selector<i.selector?-1:1;if(r.kind==="arbitrary")return 1;if(i.kind==="arbitrary")return-1;let e=this.variants.get(r.root).order,n=this.variants.get(i.root).order,s=e-n;if(s!==0)return s;if(r.kind==="compound"&&i.kind==="compound"){let c=this.compare(r.variant,i.variant);return c!==0?c:r.modifier&&i.modifier?r.modifier.value<i.modifier.value?-1:1:r.modifier?1:i.modifier?-1:0}let a=this.compareFns.get(e);if(a!==void 0)return a(r,i);if(r.root!==i.root)return r.root<i.root?-1:1;let f=r.value,u=i.value;return f===null?-1:u===null||f.kind==="arbitrary"&&u.kind!=="arbitrary"?1:f.kind!=="arbitrary"&&u.kind==="arbitrary"||f.value<u.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(r,{kind:i,applyFn:e,compounds:n,compoundsWith:s,order:a}){let f=this.variants.get(r);f?Object.assign(f,{kind:i,applyFn:e,compounds:n}):(a===void 0&&(this.lastOrder=this.nextOrder(),a=this.lastOrder),this.variants.set(r,{kind:i,applyFn:e,order:a,compoundsWith:s,compounds:n}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function ye(t){let r=0;for(let i of t){if(i[0]==="@"){if(!i.startsWith("@media")&&!i.startsWith("@supports")&&!i.startsWith("@container"))return 0;r|=1;continue}if(i.includes("::"))return 0;r|=2}return r}function Nr(t){let r=new Et;function i(c,g,{compounds:m}={}){m=m??ye(g),r.static(c,h=>{h.nodes=g.map(v=>M(v,h.nodes))},{compounds:m})}i("*",[":is(& > *)"],{compounds:0}),i("**",[":is(& *)"],{compounds:0});function e(c,g){return g.map(m=>{m=m.trim();let h=K(m," ");return h[0]==="not"?h.slice(1).join(" "):c==="@container"?h[0][0]==="("?`not ${m}`:h[1]==="not"?`${h[0]} ${h.slice(2).join(" ")}`:`${h[0]} not ${h.slice(1).join(" ")}`:`not ${m}`})}let n=["@media","@supports","@container"];function s(c){for(let g of n){if(g!==c.name)continue;let m=K(c.params,",");return m.length>1?null:(m=e(c.name,m),j(c.name,m.join(", ")))}return null}function a(c){return c.includes("::")?null:`&:not(${K(c,",").map(m=>(m=m.replaceAll("&","*"),m)).join(", ")})`}r.compound("not",3,(c,g)=>{if(g.variant.kind==="arbitrary"&&g.variant.relative||g.modifier)return null;let m=!1;if(I([c],(h,{path:v})=>{if(h.kind!=="rule"&&h.kind!=="at-rule")return 0;if(h.nodes.length>0)return 0;let w=[],A=[];for(let y of v)y.kind==="at-rule"?w.push(y):y.kind==="rule"&&A.push(y);if(w.length>1)return 2;if(A.length>1)return 2;let b=[];for(let y of A){let S=a(y.selector);if(!S)return m=!1,2;b.push(F(S,[]))}for(let y of w){let S=s(y);if(!S)return m=!1,2;b.push(S)}return Object.assign(c,F("&",b)),m=!0,1}),c.kind==="rule"&&c.selector==="&"&&c.nodes.length===1&&Object.assign(c,c.nodes[0]),!m)return null}),r.suggest("not",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("not",c))),r.compound("group",2,(c,g)=>{if(g.variant.kind==="arbitrary"&&g.variant.relative)return null;let m=g.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}group\\/${g.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}group)`,h=!1;if(I([c],(v,{path:w})=>{if(v.kind!=="rule")return 0;for(let b of w.slice(0,-1))if(b.kind==="rule")return h=!1,2;let A=v.selector.replaceAll("&",m);K(A,",").length>1&&(A=`:is(${A})`),v.selector=`&:is(${A} *)`,h=!0}),!h)return null}),r.suggest("group",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("group",c))),r.compound("peer",2,(c,g)=>{if(g.variant.kind==="arbitrary"&&g.variant.relative)return null;let m=g.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}peer\\/${g.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}peer)`,h=!1;if(I([c],(v,{path:w})=>{if(v.kind!=="rule")return 0;for(let b of w.slice(0,-1))if(b.kind==="rule")return h=!1,2;let A=v.selector.replaceAll("&",m);K(A,",").length>1&&(A=`:is(${A})`),v.selector=`&:is(${A} ~ *)`,h=!0}),!h)return null}),r.suggest("peer",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("peer",c))),i("first-letter",["&::first-letter"]),i("first-line",["&::first-line"]),i("marker",["& *::marker","&::marker","& *::-webkit-details-marker","&::-webkit-details-marker"]),i("selection",["& *::selection","&::selection"]),i("file",["&::file-selector-button"]),i("placeholder",["&::placeholder"]),i("backdrop",["&::backdrop"]),i("details-content",["&::details-content"]);{let c=function(){return D([j("@property","--tw-content",[l("syntax",'"*"'),l("initial-value",'""'),l("inherits","false")])])};var f=c;r.static("before",g=>{g.nodes=[F("&::before",[c(),l("content","var(--tw-content)"),...g.nodes])]},{compounds:0}),r.static("after",g=>{g.nodes=[F("&::after",[c(),l("content","var(--tw-content)"),...g.nodes])]},{compounds:0})}i("first",["&:first-child"]),i("last",["&:last-child"]),i("only",["&:only-child"]),i("odd",["&:nth-child(odd)"]),i("even",["&:nth-child(even)"]),i("first-of-type",["&:first-of-type"]),i("last-of-type",["&:last-of-type"]),i("only-of-type",["&:only-of-type"]),i("visited",["&:visited"]),i("target",["&:target"]),i("open",["&:is([open], :popover-open, :open)"]),i("default",["&:default"]),i("checked",["&:checked"]),i("indeterminate",["&:indeterminate"]),i("placeholder-shown",["&:placeholder-shown"]),i("autofill",["&:autofill"]),i("optional",["&:optional"]),i("required",["&:required"]),i("valid",["&:valid"]),i("invalid",["&:invalid"]),i("user-valid",["&:user-valid"]),i("user-invalid",["&:user-invalid"]),i("in-range",["&:in-range"]),i("out-of-range",["&:out-of-range"]),i("read-only",["&:read-only"]),i("empty",["&:empty"]),i("focus-within",["&:focus-within"]),r.static("hover",c=>{c.nodes=[F("&:hover",[j("@media","(hover: hover)",c.nodes)])]}),i("focus",["&:focus"]),i("focus-visible",["&:focus-visible"]),i("active",["&:active"]),i("enabled",["&:enabled"]),i("disabled",["&:disabled"]),i("inert",["&:is([inert], [inert] *)"]),r.compound("in",2,(c,g)=>{if(g.modifier)return null;let m=!1;if(I([c],(h,{path:v})=>{if(h.kind!=="rule")return 0;for(let w of v.slice(0,-1))if(w.kind==="rule")return m=!1,2;h.selector=`:where(${h.selector.replaceAll("&","*")}) &`,m=!0}),!m)return null}),r.suggest("in",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("in",c))),r.compound("has",2,(c,g)=>{if(g.modifier)return null;let m=!1;if(I([c],(h,{path:v})=>{if(h.kind!=="rule")return 0;for(let w of v.slice(0,-1))if(w.kind==="rule")return m=!1,2;h.selector=`&:has(${h.selector.replaceAll("&","*")})`,m=!0}),!m)return null}),r.suggest("has",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("has",c))),r.functional("aria",(c,g)=>{if(!g.value||g.modifier)return null;g.value.kind==="arbitrary"?c.nodes=[F(`&[aria-${Cr(g.value.value)}]`,c.nodes)]:c.nodes=[F(`&[aria-${g.value.value}="true"]`,c.nodes)]}),r.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),r.functional("data",(c,g)=>{if(!g.value||g.modifier)return null;c.nodes=[F(`&[data-${Cr(g.value.value)}]`,c.nodes)]}),r.functional("nth",(c,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!T(g.value.value))return null;c.nodes=[F(`&:nth-child(${g.value.value})`,c.nodes)]}),r.functional("nth-last",(c,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!T(g.value.value))return null;c.nodes=[F(`&:nth-last-child(${g.value.value})`,c.nodes)]}),r.functional("nth-of-type",(c,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!T(g.value.value))return null;c.nodes=[F(`&:nth-of-type(${g.value.value})`,c.nodes)]}),r.functional("nth-last-of-type",(c,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!T(g.value.value))return null;c.nodes=[F(`&:nth-last-of-type(${g.value.value})`,c.nodes)]}),r.functional("supports",(c,g)=>{if(!g.value||g.modifier)return null;let m=g.value.value;if(m===null)return null;if(/^[\w-]*\s*\(/.test(m)){let h=m.replace(/\b(and|or|not)\b/g," $1 ");c.nodes=[j("@supports",h,c.nodes)];return}m.includes(":")||(m=`${m}: var(--tw)`),(m[0]!=="("||m[m.length-1]!==")")&&(m=`(${m})`),c.nodes=[j("@supports",m,c.nodes)]},{compounds:1}),i("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),i("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),i("contrast-more",["@media (prefers-contrast: more)"]),i("contrast-less",["@media (prefers-contrast: less)"]);{let c=function(g,m,h,v){if(g===m)return 0;let w=v.get(g);if(w===null)return h==="asc"?-1:1;let A=v.get(m);return A===null?h==="asc"?1:-1:we(w,A,h)};var u=c;{let g=t.namespace("--breakpoint"),m=new q(h=>{switch(h.kind){case"static":return t.resolveValue(h.root,["--breakpoint"])??null;case"functional":{if(!h.value||h.modifier)return null;let v=null;return h.value.kind==="arbitrary"?v=h.value.value:h.value.kind==="named"&&(v=t.resolveValue(h.value.value,["--breakpoint"])),!v||v.includes("var(")?null:v}case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("max",(h,v)=>{if(v.modifier)return null;let w=m.get(v);if(w===null)return null;h.nodes=[j("@media",`(width < ${w})`,h.nodes)]},{compounds:1})},(h,v)=>c(h,v,"desc",m)),r.suggest("max",()=>Array.from(g.keys()).filter(h=>h!==null)),r.group(()=>{for(let[h,v]of t.namespace("--breakpoint"))h!==null&&r.static(h,w=>{w.nodes=[j("@media",`(width >= ${v})`,w.nodes)]},{compounds:1});r.functional("min",(h,v)=>{if(v.modifier)return null;let w=m.get(v);if(w===null)return null;h.nodes=[j("@media",`(width >= ${w})`,h.nodes)]},{compounds:1})},(h,v)=>c(h,v,"asc",m)),r.suggest("min",()=>Array.from(g.keys()).filter(h=>h!==null))}{let g=t.namespace("--container"),m=new q(h=>{switch(h.kind){case"functional":{if(h.value===null)return null;let v=null;return h.value.kind==="arbitrary"?v=h.value.value:h.value.kind==="named"&&(v=t.resolveValue(h.value.value,["--container"])),!v||v.includes("var(")?null:v}case"static":case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("@max",(h,v)=>{let w=m.get(v);if(w===null)return null;h.nodes=[j("@container",v.modifier?`${v.modifier.value} (width < ${w})`:`(width < ${w})`,h.nodes)]},{compounds:1})},(h,v)=>c(h,v,"desc",m)),r.suggest("@max",()=>Array.from(g.keys()).filter(h=>h!==null)),r.group(()=>{r.functional("@",(h,v)=>{let w=m.get(v);if(w===null)return null;h.nodes=[j("@container",v.modifier?`${v.modifier.value} (width >= ${w})`:`(width >= ${w})`,h.nodes)]},{compounds:1}),r.functional("@min",(h,v)=>{let w=m.get(v);if(w===null)return null;h.nodes=[j("@container",v.modifier?`${v.modifier.value} (width >= ${w})`:`(width >= ${w})`,h.nodes)]},{compounds:1})},(h,v)=>c(h,v,"asc",m)),r.suggest("@min",()=>Array.from(g.keys()).filter(h=>h!==null)),r.suggest("@",()=>Array.from(g.keys()).filter(h=>h!==null))}}return i("portrait",["@media (orientation: portrait)"]),i("landscape",["@media (orientation: landscape)"]),i("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),i("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),i("dark",["@media (prefers-color-scheme: dark)"]),i("starting",["@starting-style"]),i("print",["@media print"]),i("forced-colors",["@media (forced-colors: active)"]),i("inverted-colors",["@media (inverted-colors: inverted)"]),i("pointer-none",["@media (pointer: none)"]),i("pointer-coarse",["@media (pointer: coarse)"]),i("pointer-fine",["@media (pointer: fine)"]),i("any-pointer-none",["@media (any-pointer: none)"]),i("any-pointer-coarse",["@media (any-pointer: coarse)"]),i("any-pointer-fine",["@media (any-pointer: fine)"]),i("noscript",["@media (scripting: none)"]),r}function Cr(t){if(t.includes("=")){let[r,...i]=K(t,"="),e=i.join("=").trim();if(e[0]==="'"||e[0]==='"')return t;if(e.length>1){let n=e[e.length-1];if(e[e.length-2]===" "&&(n==="i"||n==="I"||n==="s"||n==="S"))return`${r}="${e.slice(0,-2)}" ${n}`}return`${r}="${e}"`}return t}function Rt(t,r){I(t,(i,{replaceWith:e})=>{if(i.kind==="at-rule"&&i.name==="@slot")e(r);else if(i.kind==="at-rule"&&(i.name==="@keyframes"||i.name==="@property"))return Object.assign(i,D([j(i.name,i.params,i.nodes)])),1})}function $r(t){let r=vr(t),i=Nr(t),e=new q(u=>sr(u,f)),n=new q(u=>Array.from(ar(u,f))),s=new q(u=>{let c=Vr(u,f);try{Ne(c.map(({node:g})=>g),f)}catch{return[]}return c}),a=new q(u=>{for(let c of Ge(u))t.markUsedVariable(c)}),f={theme:t,utilities:r,variants:i,invalidCandidates:new Set,important:!1,candidatesToCss(u){let c=[];for(let g of u){let m=!1,{astNodes:h}=pe([g],this,{onInvalidCandidate(){m=!0}});h=ve(h,f,0),h.length===0||m?c.push(null):c.push(te(h))}return c},getClassOrder(u){return Ar(this,u)},getClassList(){return br(this)},getVariants(){return xr(this)},parseCandidate(u){return n.get(u)},parseVariant(u){return e.get(u)},compileAstNodes(u){return s.get(u)},getVariantOrder(){let u=Array.from(e.values());u.sort((h,v)=>this.variants.compare(h,v));let c=new Map,g,m=0;for(let h of u)h!==null&&(g!==void 0&&this.variants.compare(g,h)!==0&&m++,c.set(h,m),g=h);return c},resolveThemeValue(u,c=!0){let g=u.lastIndexOf("/"),m=null;g!==-1&&(m=u.slice(g+1).trim(),u=u.slice(0,g).trim());let h=t.resolve(null,[u],c?1:0)??void 0;return m&&h?Y(h,m):h},trackUsedVariables(u){a.get(u)}};return f}var Ot=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","--tw-translate-z","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","mask-image","--tw-mask-top","--tw-mask-top-from-color","--tw-mask-top-from-position","--tw-mask-top-to-color","--tw-mask-top-to-position","--tw-mask-right","--tw-mask-right-from-color","--tw-mask-right-from-position","--tw-mask-right-to-color","--tw-mask-right-to-position","--tw-mask-bottom","--tw-mask-bottom-from-color","--tw-mask-bottom-from-position","--tw-mask-bottom-to-color","--tw-mask-bottom-to-position","--tw-mask-left","--tw-mask-left-from-color","--tw-mask-left-from-position","--tw-mask-left-to-color","--tw-mask-left-to-position","--tw-mask-linear","--tw-mask-linear-position","--tw-mask-linear-from-color","--tw-mask-linear-from-position","--tw-mask-linear-to-color","--tw-mask-linear-to-position","--tw-mask-radial","--tw-mask-radial-shape","--tw-mask-radial-size","--tw-mask-radial-position","--tw-mask-radial-from-color","--tw-mask-radial-from-position","--tw-mask-radial-to-color","--tw-mask-radial-to-position","--tw-mask-conic","--tw-mask-conic-position","--tw-mask-conic-from-color","--tw-mask-conic-from-position","--tw-mask-conic-to-color","--tw-mask-conic-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","mask-composite","mask-mode","mask-type","mask-size","mask-clip","mask-position","mask-repeat","mask-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function pe(t,r,{onInvalidCandidate:i}={}){let e=new Map,n=[],s=new Map;for(let f of t){if(r.invalidCandidates.has(f)){i?.(f);continue}let u=r.parseCandidate(f);if(u.length===0){i?.(f);continue}s.set(f,u)}let a=r.getVariantOrder();for(let[f,u]of s){let c=!1;for(let g of u){let m=r.compileAstNodes(g);if(m.length!==0){c=!0;for(let{node:h,propertySort:v}of m){let w=0n;for(let A of g.variants)w|=1n<<BigInt(a.get(A));e.set(h,{properties:v,variants:w,candidate:f}),n.push(h)}}}c||i?.(f)}return n.sort((f,u)=>{let c=e.get(f),g=e.get(u);if(c.variants-g.variants!==0n)return Number(c.variants-g.variants);let m=0;for(;m<c.properties.order.length&&m<g.properties.order.length&&c.properties.order[m]===g.properties.order[m];)m+=1;return(c.properties.order[m]??1/0)-(g.properties.order[m]??1/0)||g.properties.count-c.properties.count||nt(c.candidate,g.candidate)}),{astNodes:n,nodeSorting:e}}function Vr(t,r){let i=dn(t,r);if(i.length===0)return[];let e=[],n=`.${ue(t.raw)}`;for(let s of i){let a=mn(s);(t.important||r.important)&&Tr(s);let f={kind:"rule",selector:n,nodes:s};for(let u of t.variants)if(Ve(f,u,r.variants)===null)return[];e.push({node:f,propertySort:a})}return e}function Ve(t,r,i,e=0){if(r.kind==="arbitrary"){if(r.relative&&e===0)return null;t.nodes=[M(r.selector,t.nodes)];return}let{applyFn:n}=i.get(r.root);if(r.kind==="compound"){let a=j("@slot");if(Ve(a,r.variant,i,e+1)===null||r.root==="not"&&a.nodes.length>1)return null;for(let u of a.nodes)if(u.kind!=="rule"&&u.kind!=="at-rule"||n(u,r)===null)return null;I(a.nodes,u=>{if((u.kind==="rule"||u.kind==="at-rule")&&u.nodes.length<=0)return u.nodes=t.nodes,1}),t.nodes=a.nodes;return}if(n(t,r)===null)return null}function Sr(t){let r=t.options?.types??[];return r.length>1&&r.includes("any")}function dn(t,r){if(t.kind==="arbitrary"){let a=t.value;return t.modifier&&(a=Q(a,t.modifier,r.theme)),a===null?[]:[[l(t.property,a)]]}let i=r.utilities.get(t.root)??[],e=[],n=i.filter(a=>!Sr(a));for(let a of n){if(a.kind!==t.kind)continue;let f=a.compileFn(t);if(f!==void 0){if(f===null)return e;e.push(f)}}if(e.length>0)return e;let s=i.filter(a=>Sr(a));for(let a of s){if(a.kind!==t.kind)continue;let f=a.compileFn(t);if(f!==void 0){if(f===null)return e;e.push(f)}}return e}function Tr(t){for(let r of t)r.kind!=="at-root"&&(r.kind==="declaration"?r.important=!0:(r.kind==="rule"||r.kind==="at-rule")&&Tr(r.nodes))}function mn(t){let r=new Set,i=0,e=t.slice(),n=!1;for(;e.length>0;){let s=e.shift();if(s.kind==="declaration"){if(s.value===void 0||(i++,n))continue;if(s.property==="--tw-sort"){let f=Ot.indexOf(s.value??"");if(f!==-1){r.add(f),n=!0;continue}}let a=Ot.indexOf(s.property);a!==-1&&r.add(a)}else if(s.kind==="rule"||s.kind==="at-rule")for(let a of s.nodes)e.push(a)}return{order:Array.from(r).sort((s,a)=>s-a),count:i}}function Ue(t,r){let i=0,e=M("&",t),n=new Set,s=new q(()=>new Set),a=new q(()=>new Set);I([e],(m,{parent:h})=>{if(m.kind==="at-rule"){if(m.name==="@keyframes")return I(m.nodes,v=>{if(v.kind==="at-rule"&&v.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(m.name==="@utility"){let v=m.params.replace(/-\*$/,"");a.get(v).add(m),I(m.nodes,w=>{if(!(w.kind!=="at-rule"||w.name!=="@apply")){n.add(m);for(let A of Er(w,r))s.get(m).add(A)}});return}if(m.name==="@apply"){if(h===null)return;i|=1,n.add(h);for(let v of Er(m,r))s.get(h).add(v)}}});let f=new Set,u=[],c=new Set;function g(m,h=[]){if(!f.has(m)){if(c.has(m)){let v=h[(h.indexOf(m)+1)%h.length];throw m.kind==="at-rule"&&m.name==="@utility"&&v.kind==="at-rule"&&v.name==="@utility"&&I(m.nodes,w=>{if(w.kind!=="at-rule"||w.name!=="@apply")return;let A=w.params.split(/\s+/g);for(let b of A)for(let y of r.parseCandidate(b))switch(y.kind){case"arbitrary":break;case"static":case"functional":if(v.params.replace(/-\*$/,"")===y.root)throw new Error(`You cannot \`@apply\` the \`${b}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${te([m])}
Relies on:

${te([v])}`)}c.add(m);for(let v of s.get(m))for(let w of a.get(v))h.push(m),g(w,h),h.pop();f.add(m),c.delete(m),u.push(m)}}for(let m of n)g(m);for(let m of u)if("nodes"in m)for(let h=0;h<m.nodes.length;h++){let v=m.nodes[h];if(v.kind!=="at-rule"||v.name!=="@apply")continue;let w=v.params.split(/\s+/g);{let A=pe(w,r,{onInvalidCandidate:y=>{throw new Error(`Cannot apply unknown utility class: ${y}`)}}).astNodes,b=[];for(let y of A)if(y.kind==="rule")for(let S of y.nodes)b.push(S);else b.push(y);m.nodes.splice(h,1,...b)}}return i}function*Er(t,r){for(let i of t.params.split(/\s+/g))for(let e of r.parseCandidate(i))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function Pt(t,r,i,e=0){let n=0,s=[];return I(t,(a,{replaceWith:f})=>{if(a.kind==="at-rule"&&(a.name==="@import"||a.name==="@reference")){let u=gn(H(a.params));if(u===null)return;a.name==="@reference"&&(u.media="reference"),n|=2;let{uri:c,layer:g,media:m,supports:h}=u;if(c.startsWith("data:")||c.startsWith("http://")||c.startsWith("https://"))return;let v=ne({},[]);return s.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${c}\` in \`${r}\`)`);let w=await i(c,r),A=ge(w.content);await Pt(A,w.base,i,e+1),v.nodes=hn([ne({base:w.base},A)],g,m,h)})()),f(v),1}}),s.length>0&&await Promise.all(s),n}function gn(t){let r,i=null,e=null,n=null;for(let s=0;s<t.length;s++){let a=t[s];if(a.kind!=="separator"){if(a.kind==="word"&&!r){if(!a.value||a.value[0]!=='"'&&a.value[0]!=="'")return null;r=a.value.slice(1,-1);continue}if(a.kind==="function"&&a.value.toLowerCase()==="url"||!r)return null;if((a.kind==="word"||a.kind==="function")&&a.value.toLowerCase()==="layer"){if(i)return null;if(n)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in a?i=J(a.nodes):i="";continue}if(a.kind==="function"&&a.value.toLowerCase()==="supports"){if(n)return null;n=J(a.nodes);continue}e=J(t.slice(s));break}}return r?{uri:r,layer:i,media:e,supports:n}:null}function hn(t,r,i,e){let n=t;return r!==null&&(n=[j("@layer",r,n)]),i!==null&&(n=[j("@media",i,n)]),e!==null&&(n=[j("@supports",e[0]==="("?e:`(${e})`,n)]),n}function Se(t,r=null){return Array.isArray(t)&&t.length===2&&typeof t[1]=="object"&&typeof t[1]!==null?r?t[1][r]??null:t[0]:Array.isArray(t)&&r===null?t.join(", "):typeof t=="string"&&r===null?t:null}function Rr(t,{theme:r},i){for(let e of i){let n=lt([e]);n&&t.theme.clearNamespace(`--${n}`,4)}for(let[e,n]of vn(r)){if(typeof n!="string"&&typeof n!="number")continue;if(typeof n=="string"&&(n=n.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof n=="number"||typeof n=="string")){let a=typeof n=="string"?parseFloat(n):n;a>=0&&a<=1&&(n=a*100+"%")}let s=lt(e);s&&t.theme.add(`--${s}`,""+n,7)}if(Object.hasOwn(r,"fontFamily")){let e=5;{let n=Se(r.fontFamily.sans);n&&t.theme.hasDefault("--font-sans")&&(t.theme.add("--default-font-family",n,e),t.theme.add("--default-font-feature-settings",Se(r.fontFamily.sans,"fontFeatureSettings")??"normal",e),t.theme.add("--default-font-variation-settings",Se(r.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let n=Se(r.fontFamily.mono);n&&t.theme.hasDefault("--font-mono")&&(t.theme.add("--default-mono-font-family",n,e),t.theme.add("--default-mono-font-feature-settings",Se(r.fontFamily.mono,"fontFeatureSettings")??"normal",e),t.theme.add("--default-mono-font-variation-settings",Se(r.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return r}function vn(t){let r=[];return Or(t,[],(i,e)=>{if(kn(i))return r.push([e,i]),1;if(yn(i)){r.push([e,i[0]]);for(let n of Reflect.ownKeys(i[1]))r.push([[...e,`-${n}`],i[1][n]]);return 1}if(Array.isArray(i)&&i.every(n=>typeof n=="string"))return r.push([e,i.join(", ")]),1}),r}var wn=/^[a-zA-Z0-9-_%/\.]+$/;function lt(t){if(t[0]==="container")return null;t=structuredClone(t),t[0]==="animation"&&(t[0]="animate"),t[0]==="aspectRatio"&&(t[0]="aspect"),t[0]==="borderRadius"&&(t[0]="radius"),t[0]==="boxShadow"&&(t[0]="shadow"),t[0]==="colors"&&(t[0]="color"),t[0]==="containers"&&(t[0]="container"),t[0]==="fontFamily"&&(t[0]="font"),t[0]==="fontSize"&&(t[0]="text"),t[0]==="letterSpacing"&&(t[0]="tracking"),t[0]==="lineHeight"&&(t[0]="leading"),t[0]==="maxWidth"&&(t[0]="container"),t[0]==="screens"&&(t[0]="breakpoint"),t[0]==="transitionTimingFunction"&&(t[0]="ease");for(let r of t)if(!wn.test(r))return null;return t.map((r,i,e)=>r==="1"&&i!==e.length-1?"":r).map(r=>r.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(i,e,n)=>`${e}-${n.toLowerCase()}`)).filter((r,i)=>r!=="DEFAULT"||i!==t.length-1).join("-")}function kn(t){return typeof t=="number"||typeof t=="string"}function yn(t){if(!Array.isArray(t)||t.length!==2||typeof t[0]!="string"&&typeof t[0]!="number"||t[1]===void 0||t[1]===null||typeof t[1]!="object")return!1;for(let r of Reflect.ownKeys(t[1]))if(typeof r!="string"||typeof t[1][r]!="string"&&typeof t[1][r]!="number")return!1;return!0}function Or(t,r=[],i){for(let e of Reflect.ownKeys(t)){let n=t[e];if(n==null)continue;let s=[...r,e],a=i(n,s)??0;if(a!==1){if(a===2)return 2;if(!(!Array.isArray(n)&&typeof n!="object")&&Or(n,s,i)===2)return 2}}}function at(t){let r=[];for(let i of K(t,".")){if(!i.includes("[")){r.push(i);continue}let e=0;for(;;){let n=i.indexOf("[",e),s=i.indexOf("]",n);if(n===-1||s===-1)break;n>e&&r.push(i.slice(e,n)),r.push(i.slice(n+1,s)),e=s+1}e<=i.length-1&&r.push(i.slice(e))}return r}function Te(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let r=Object.getPrototypeOf(t);return r===null||Object.getPrototypeOf(r)===null}function De(t,r,i,e=[]){for(let n of r)if(n!=null)for(let s of Reflect.ownKeys(n)){e.push(s);let a=i(t[s],n[s],e);a!==void 0?t[s]=a:!Te(t[s])||!Te(n[s])?t[s]=n[s]:t[s]=De({},[t[s],n[s]],i,e),e.pop()}return t}function st(t,r,i){return function(n,s){let a=n.lastIndexOf("/"),f=null;a!==-1&&(f=n.slice(a+1).trim(),n=n.slice(0,a).trim());let u=(()=>{let c=at(n),[g,m]=bn(t.theme,c),h=i(Pr(r()??{},c)??null);if(typeof h=="string"&&(h=h.replace("<alpha-value>","1")),typeof g!="object")return typeof m!="object"&&m&4?h??g:g;if(h!==null&&typeof h=="object"&&!Array.isArray(h)){let v=De({},[h],(w,A)=>A);if(g===null&&Object.hasOwn(h,"__CSS_VALUES__")){let w={};for(let A in h.__CSS_VALUES__)w[A]=h[A],delete v[A];g=w}for(let w in g)w!=="__CSS_VALUES__"&&(h?.__CSS_VALUES__?.[w]&4&&Pr(v,w.split("-"))!==void 0||(v[he(w)]=g[w]));return v}if(Array.isArray(g)&&Array.isArray(m)&&Array.isArray(h)){let v=g[0],w=g[1];m[0]&4&&(v=h[0]??v);for(let A of Object.keys(w))m[1][A]&4&&(w[A]=h[1][A]??w[A]);return[v,w]}return g??h})();return f&&typeof u=="string"&&(u=Y(u,f)),u??s}}function bn(t,r){if(r.length===1&&r[0].startsWith("--"))return[t.get([r[0]]),t.getOptions(r[0])];let i=lt(r),e=new Map,n=new q(()=>new Map),s=t.namespace(`--${i}`);if(s.size===0)return[null,0];let a=new Map;for(let[g,m]of s){if(!g||!g.includes("--")){e.set(g,m),a.set(g,t.getOptions(g?`--${i}-${g}`:`--${i}`));continue}let h=g.indexOf("--"),v=g.slice(0,h),w=g.slice(h+2);w=w.replace(/-([a-z])/g,(A,b)=>b.toUpperCase()),n.get(v===""?null:v).set(w,[m,t.getOptions(`--${i}${g}`)])}let f=t.getOptions(`--${i}`);for(let[g,m]of n){let h=e.get(g);if(typeof h!="string")continue;let v={},w={};for(let[A,[b,y]]of m)v[A]=b,w[A]=y;e.set(g,[h,v]),a.set(g,[f,w])}let u={},c={};for(let[g,m]of e)_r(u,[g??"DEFAULT"],m);for(let[g,m]of a)_r(c,[g??"DEFAULT"],m);return r[r.length-1]==="DEFAULT"?[u?.DEFAULT??null,c.DEFAULT??0]:"DEFAULT"in u&&Object.keys(u).length===1?[u.DEFAULT,c.DEFAULT??0]:(u.__CSS_VALUES__=c,[u,c])}function Pr(t,r){for(let i=0;i<r.length;++i){let e=r[i];if(t?.[e]===void 0){if(r[i+1]===void 0)return;r[i+1]=`${e}-${r[i+1]}`;continue}t=t[e]}return t}function _r(t,r,i){for(let e of r.slice(0,-1))t[e]===void 0&&(t[e]={}),t=t[e];t[r[r.length-1]]=i}function xn(t){return{kind:"combinator",value:t}}function An(t,r){return{kind:"function",value:t,nodes:r}}function je(t){return{kind:"selector",value:t}}function Cn(t){return{kind:"separator",value:t}}function Nn(t){return{kind:"value",value:t}}function Ie(t,r,i=null){for(let e=0;e<t.length;e++){let n=t[e],s=!1,a=0,f=r(n,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(t.splice(e,1),a=0):u.length===1?(t[e]=u[0],a=1):(t.splice(e,1,...u),a=u.length):(t[e]=u,a=1))}})??0;if(s){f===0?e--:e+=a-1;continue}if(f===2)return 2;if(f!==1&&n.kind==="function"&&Ie(n.nodes,r,n)===2)return 2}}function Fe(t){let r="";for(let i of t)switch(i.kind){case"combinator":case"selector":case"separator":case"value":{r+=i.value;break}case"function":r+=i.value+"("+Fe(i.nodes)+")"}return r}var Kr=92,$n=93,Ur=41,Vn=58,Dr=44,Sn=34,Tn=46,jr=62,Ir=10,En=35,Fr=91,zr=40,Lr=43,Rn=39,Mr=32,Wr=9,Br=126;function ut(t){t=t.replaceAll(`\r
`,`
`);let r=[],i=[],e=null,n="",s;for(let a=0;a<t.length;a++){let f=t.charCodeAt(a);switch(f){case Dr:case jr:case Ir:case Mr:case Lr:case Wr:case Br:{if(n.length>0){let h=je(n);e?e.nodes.push(h):r.push(h),n=""}let u=a,c=a+1;for(;c<t.length&&(s=t.charCodeAt(c),!(s!==Dr&&s!==jr&&s!==Ir&&s!==Mr&&s!==Lr&&s!==Wr&&s!==Br));c++);a=c-1;let g=t.slice(u,c),m=g.trim()===","?Cn(g):xn(g);e?e.nodes.push(m):r.push(m);break}case zr:{let u=An(n,[]);if(n="",u.value!==":not"&&u.value!==":where"&&u.value!==":has"&&u.value!==":is"){let c=a+1,g=0;for(let h=a+1;h<t.length;h++){if(s=t.charCodeAt(h),s===zr){g++;continue}if(s===Ur){if(g===0){a=h;break}g--}}let m=a;u.nodes.push(Nn(t.slice(c,m))),n="",a=m,e?e.nodes.push(u):r.push(u);break}e?e.nodes.push(u):r.push(u),i.push(u),e=u;break}case Ur:{let u=i.pop();if(n.length>0){let c=je(n);u.nodes.push(c),n=""}i.length>0?e=i[i.length-1]:e=null;break}case Tn:case Vn:case En:{if(n.length>0){let u=je(n);e?e.nodes.push(u):r.push(u)}n=String.fromCharCode(f);break}case Fr:{if(n.length>0){let g=je(n);e?e.nodes.push(g):r.push(g)}n="";let u=a,c=0;for(let g=a+1;g<t.length;g++){if(s=t.charCodeAt(g),s===Fr){c++;continue}if(s===$n){if(c===0){a=g;break}c--}}n+=t.slice(u,a+1);break}case Rn:case Sn:{let u=a;for(let c=a+1;c<t.length;c++)if(s=t.charCodeAt(c),s===Kr)c+=1;else if(s===f){a=c;break}n+=t.slice(u,a+1);break}case Kr:{let u=t.charCodeAt(a+1);n+=String.fromCharCode(f)+String.fromCharCode(u),a+=1;break}default:n+=String.fromCharCode(f)}}return n.length>0&&r.push(je(n)),r}var qr=/^[a-z@][a-zA-Z0-9/%._-]*$/;function _t({designSystem:t,ast:r,resolvedConfig:i,featuresRef:e,referenceMode:n}){let s={addBase(a){if(n)return;let f=oe(a);e.current|=Ne(f,t),r.push(j("@layer","base",f))},addVariant(a,f){if(!ot.test(a))throw new Error(`\`addVariant('${a}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);typeof f=="string"||Array.isArray(f)?t.variants.static(a,u=>{u.nodes=Hr(f,u.nodes)},{compounds:ye(typeof f=="string"?[f]:f)}):typeof f=="object"&&t.variants.fromAst(a,oe(f))},matchVariant(a,f,u){function c(m,h,v){let w=f(m,{modifier:h?.value??null});return Hr(w,v)}let g=Object.keys(u?.values??{});t.variants.group(()=>{t.variants.functional(a,(m,h)=>{if(!h.value){if(u?.values&&"DEFAULT"in u.values){m.nodes=c(u.values.DEFAULT,h.modifier,m.nodes);return}return null}if(h.value.kind==="arbitrary")m.nodes=c(h.value.value,h.modifier,m.nodes);else if(h.value.kind==="named"&&u?.values){let v=u.values[h.value.value];if(typeof v!="string")return;m.nodes=c(v,h.modifier,m.nodes)}})},(m,h)=>{if(m.kind!=="functional"||h.kind!=="functional")return 0;let v=m.value?m.value.value:"DEFAULT",w=h.value?h.value.value:"DEFAULT",A=u?.values?.[v]??v,b=u?.values?.[w]??w;if(u&&typeof u.sort=="function")return u.sort({value:A,modifier:m.modifier?.value??null},{value:b,modifier:h.modifier?.value??null});let y=g.indexOf(v),S=g.indexOf(w);return y=y===-1?g.length:y,S=S===-1?g.length:S,y!==S?y-S:A<b?-1:1})},addUtilities(a){a=Array.isArray(a)?a:[a];let f=a.flatMap(c=>Object.entries(c));f=f.flatMap(([c,g])=>K(c,",").map(m=>[m.trim(),g]));let u=new q(()=>[]);for(let[c,g]of f){if(c.startsWith("@keyframes ")){n||r.push(M(c,oe(g)));continue}let m=ut(c),h=!1;if(Ie(m,v=>{if(v.kind==="selector"&&v.value[0]==="."&&qr.test(v.value.slice(1))){let w=v.value;v.value="&";let A=Fe(m),b=w.slice(1),y=A==="&"?oe(g):[M(A,oe(g))];u.get(b).push(...y),h=!0,v.value=w;return}if(v.kind==="function"&&v.value===":not")return 1}),!h)throw new Error(`\`addUtilities({ '${c}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[c,g]of u)t.theme.prefix&&I(g,m=>{if(m.kind==="rule"){let h=ut(m.selector);Ie(h,v=>{v.kind==="selector"&&v.value[0]==="."&&(v.value=`.${t.theme.prefix}\\:${v.value.slice(1)}`)}),m.selector=Fe(h)}}),t.utilities.static(c,m=>{let h=structuredClone(g);return Gr(h,c,m.raw),e.current|=Ue(h,t),h})},matchUtilities(a,f){let u=f?.type?Array.isArray(f?.type)?f.type:[f.type]:["any"];for(let[g,m]of Object.entries(a)){let h=function({negative:v}){return w=>{if(w.value?.kind==="arbitrary"&&u.length>0&&!u.includes("any")&&(w.value.dataType&&!u.includes(w.value.dataType)||!w.value.dataType&&!W(w.value.value,u)))return;let A=u.includes("color"),b=null,y=!1;{let P=f?.values??{};A&&(P=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentcolor"},P)),w.value?w.value.kind==="arbitrary"?b=w.value.value:w.value.fraction&&P[w.value.fraction]?(b=P[w.value.fraction],y=!0):P[w.value.value]?b=P[w.value.value]:P.__BARE_VALUE__&&(b=P.__BARE_VALUE__(w.value)??null,y=(w.value.fraction!==null&&b?.includes("/"))??!1):b=P.DEFAULT??null}if(b===null)return;let S;{let P=f?.modifiers??null;w.modifier?P==="any"||w.modifier.kind==="arbitrary"?S=w.modifier.value:P?.[w.modifier.value]?S=P[w.modifier.value]:A&&!Number.isNaN(Number(w.modifier.value))?S=`${w.modifier.value}%`:S=null:S=null}if(w.modifier&&S===null&&!y)return w.value?.kind==="arbitrary"?null:void 0;A&&S!==null&&(b=Y(b,S)),v&&(b=`calc(${b} * -1)`);let E=oe(m(b,{modifier:S}));return Gr(E,g,w.raw),e.current|=Ue(E,t),E}};var c=h;if(!qr.test(g))throw new Error(`\`matchUtilities({ '${g}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);f?.supportsNegativeValues&&t.utilities.functional(`-${g}`,h({negative:!0}),{types:u}),t.utilities.functional(g,h({negative:!1}),{types:u}),t.utilities.suggest(g,()=>{let v=f?.values??{},w=new Set(Object.keys(v));w.delete("__BARE_VALUE__"),w.has("DEFAULT")&&(w.delete("DEFAULT"),w.add(null));let A=f?.modifiers??{},b=A==="any"?[]:Object.keys(A);return[{supportsNegative:f?.supportsNegativeValues??!1,values:Array.from(w),modifiers:b}]})}},addComponents(a,f){this.addUtilities(a,f)},matchComponents(a,f){this.matchUtilities(a,f)},theme:st(t,()=>i.theme??{},a=>a),prefix(a){return a},config(a,f){let u=i;if(!a)return u;let c=at(a);for(let g=0;g<c.length;++g){let m=c[g];if(u[m]===void 0)return f;u=u[m]}return u??f}};return s.addComponents=s.addComponents.bind(s),s.matchComponents=s.matchComponents.bind(s),s}function oe(t){let r=[];t=Array.isArray(t)?t:[t];let i=t.flatMap(e=>Object.entries(e));for(let[e,n]of i)if(typeof n!="object"){if(!e.startsWith("--")){if(n==="@slot"){r.push(M(e,[j("@slot")]));continue}e=e.replace(/([A-Z])/g,"-$1").toLowerCase()}r.push(l(e,String(n)))}else if(Array.isArray(n))for(let s of n)typeof s=="string"?r.push(l(e,s)):r.push(M(e,oe(s)));else n!==null&&r.push(M(e,oe(n)));return r}function Hr(t,r){return(typeof t=="string"?[t]:t).flatMap(e=>{if(e.trim().endsWith("}")){let n=e.replace("}","{@slot}}"),s=ge(n);return Rt(s,r),s}else return M(e,r)})}function Gr(t,r,i){I(t,e=>{if(e.kind==="rule"){let n=ut(e.selector);Ie(n,s=>{s.kind==="selector"&&s.value===`.${r}`&&(s.value=`.${ue(i)}`)}),e.selector=Fe(n)}})}function Yr(t,r,i){for(let e of Pn(r))t.theme.addKeyframes(e)}function Pn(t){let r=[];if("keyframes"in t.theme)for(let[i,e]of Object.entries(t.theme.keyframes))r.push(j("@keyframes",i,oe(e)));return r}var ct={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function be(t){return{__BARE_VALUE__:t}}var re=be(t=>{if(T(t.value))return t.value}),X=be(t=>{if(T(t.value))return`${t.value}%`}),de=be(t=>{if(T(t.value))return`${t.value}px`}),Jr=be(t=>{if(T(t.value))return`${t.value}ms`}),ft=be(t=>{if(T(t.value))return`${t.value}deg`}),_n=be(t=>{if(t.fraction===null)return;let[r,i]=K(t.fraction,"/");if(!(!T(r)||!T(i)))return t.fraction}),Qr=be(t=>{if(T(Number(t.value)))return`repeat(${t.value}, minmax(0, 1fr))`}),Zr={accentColor:({theme:t})=>t("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",..._n},backdropBlur:({theme:t})=>t("blur"),backdropBrightness:({theme:t})=>({...t("brightness"),...X}),backdropContrast:({theme:t})=>({...t("contrast"),...X}),backdropGrayscale:({theme:t})=>({...t("grayscale"),...X}),backdropHueRotate:({theme:t})=>({...t("hueRotate"),...ft}),backdropInvert:({theme:t})=>({...t("invert"),...X}),backdropOpacity:({theme:t})=>({...t("opacity"),...X}),backdropSaturate:({theme:t})=>({...t("saturate"),...X}),backdropSepia:({theme:t})=>({...t("sepia"),...X}),backgroundColor:({theme:t})=>t("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:t})=>t("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:t})=>({DEFAULT:"currentcolor",...t("colors")}),borderOpacity:({theme:t})=>t("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:t})=>t("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...de},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:t})=>t("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...X},caretColor:({theme:t})=>t("colors"),colors:()=>({...ct}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...re},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...X},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:t})=>t("borderColor"),divideOpacity:({theme:t})=>t("borderOpacity"),divideWidth:({theme:t})=>({...t("borderWidth"),...de}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:t})=>t("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...t("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...re},flexShrink:{0:"0",DEFAULT:"1",...re},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:t})=>t("spacing"),gradientColorStops:({theme:t})=>t("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...X},grayscale:{0:"0",DEFAULT:"100%",...X},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...re},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...re},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...re},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...re},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Qr},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Qr},height:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...ft},inset:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),invert:{0:"0",DEFAULT:"100%",...X},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:t})=>({auto:"auto",...t("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...re},maxHeight:({theme:t})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),maxWidth:({theme:t})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...t("spacing")}),minHeight:({theme:t})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),minWidth:({theme:t})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...X},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...re},outlineColor:({theme:t})=>t("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...de},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...de},padding:({theme:t})=>t("spacing"),placeholderColor:({theme:t})=>t("colors"),placeholderOpacity:({theme:t})=>t("opacity"),ringColor:({theme:t})=>({DEFAULT:"currentcolor",...t("colors")}),ringOffsetColor:({theme:t})=>t("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...de},ringOpacity:({theme:t})=>({DEFAULT:"0.5",...t("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...de},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...ft},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...X},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...X},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:t})=>t("spacing"),scrollPadding:({theme:t})=>t("spacing"),sepia:{0:"0",DEFAULT:"100%",...X},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...ft},space:({theme:t})=>t("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:t})=>({none:"none",...t("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...re},supports:{},data:{},textColor:({theme:t})=>t("colors"),textDecorationColor:({theme:t})=>t("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...de},textIndent:({theme:t})=>t("spacing"),textOpacity:({theme:t})=>t("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...de},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Jr},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Jr},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:t})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),size:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),width:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...re}};function Xr(t){return{theme:{...Zr,colors:({theme:r})=>r("color",{}),extend:{fontSize:({theme:r})=>({...r("text",{})}),boxShadow:({theme:r})=>({...r("shadow",{})}),animation:({theme:r})=>({...r("animate",{})}),aspectRatio:({theme:r})=>({...r("aspect",{})}),borderRadius:({theme:r})=>({...r("radius",{})}),screens:({theme:r})=>({...r("breakpoint",{})}),letterSpacing:({theme:r})=>({...r("tracking",{})}),lineHeight:({theme:r})=>({...r("leading",{})}),transitionDuration:{DEFAULT:t.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:t.get(["--default-transition-timing-function"])??null},maxWidth:({theme:r})=>({...r("container",{})})}}}}var Kn={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function Ut(t,r){let i={design:t,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(Kn)};for(let n of r)Kt(i,n);for(let n of i.configs)"darkMode"in n&&n.darkMode!==void 0&&(i.result.darkMode=n.darkMode??null),"prefix"in n&&n.prefix!==void 0&&(i.result.prefix=n.prefix??""),"blocklist"in n&&n.blocklist!==void 0&&(i.result.blocklist=n.blocklist??[]),"important"in n&&n.important!==void 0&&(i.result.important=n.important??!1);let e=Dn(i);return{resolvedConfig:{...i.result,content:i.content,theme:i.theme,plugins:i.plugins},replacedThemeKeys:e}}function Un(t,r){if(Array.isArray(t)&&Te(t[0]))return t.concat(r);if(Array.isArray(r)&&Te(r[0])&&Te(t))return[t,...r];if(Array.isArray(r))return r}function Kt(t,{config:r,base:i,path:e,reference:n}){let s=[];for(let u of r.plugins??[])"__isOptionsFunction"in u?s.push({...u(),reference:n}):"handler"in u?s.push({...u,reference:n}):s.push({handler:u,reference:n});if(Array.isArray(r.presets)&&r.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let u of r.presets??[])Kt(t,{path:e,base:i,config:u,reference:n});for(let u of s)t.plugins.push(u),u.config&&Kt(t,{path:e,base:i,config:u.config,reference:!!u.reference});let a=r.content??[],f=Array.isArray(a)?a:a.files;for(let u of f)t.content.files.push(typeof u=="object"?u:{base:i,pattern:u});t.configs.push(r)}function Dn(t){let r=new Set,i=st(t.design,()=>t.theme,n),e=Object.assign(i,{theme:i,colors:ct});function n(s){return typeof s=="function"?s(e)??null:s??null}for(let s of t.configs){let a=s.theme??{},f=a.extend??{};for(let u in a)u!=="extend"&&r.add(u);Object.assign(t.theme,a);for(let u in f)t.extend[u]??=[],t.extend[u].push(f[u])}delete t.theme.extend;for(let s in t.extend){let a=[t.theme[s],...t.extend[s]];t.theme[s]=()=>{let f=a.map(n);return De({},f,Un)}}for(let s in t.theme)t.theme[s]=n(t.theme[s]);if(t.theme.screens&&typeof t.theme.screens=="object")for(let s of Object.keys(t.theme.screens)){let a=t.theme.screens[s];a&&typeof a=="object"&&("raw"in a||"max"in a||"min"in a&&(t.theme.screens[s]=a.min))}return r}function ei(t,r){let i=t.theme.container||{};if(typeof i!="object"||i===null)return;let e=jn(i,r);e.length!==0&&r.utilities.static("container",()=>structuredClone(e))}function jn({center:t,padding:r,screens:i},e){let n=[],s=null;if(t&&n.push(l("margin-inline","auto")),(typeof r=="string"||typeof r=="object"&&r!==null&&"DEFAULT"in r)&&n.push(l("padding-inline",typeof r=="string"?r:r.DEFAULT)),typeof i=="object"&&i!==null){s=new Map;let a=Array.from(e.theme.namespace("--breakpoint").entries());if(a.sort((f,u)=>we(f[1],u[1],"asc")),a.length>0){let[f]=a[0];n.push(j("@media",`(width >= --theme(--breakpoint-${f}))`,[l("max-width","none")]))}for(let[f,u]of Object.entries(i)){if(typeof u=="object")if("min"in u)u=u.min;else continue;s.set(f,j("@media",`(width >= ${u})`,[l("max-width",u)]))}}if(typeof r=="object"&&r!==null){let a=Object.entries(r).filter(([f])=>f!=="DEFAULT").map(([f,u])=>[f,e.theme.resolveValue(f,["--breakpoint"]),u]).filter(Boolean);a.sort((f,u)=>we(f[1],u[1],"asc"));for(let[f,,u]of a)if(s&&s.has(f))s.get(f).nodes.push(l("padding-inline",u));else{if(s)continue;n.push(j("@media",`(width >= theme(--breakpoint-${f}))`,[l("padding-inline",u)]))}}if(s)for(let[,a]of s)n.push(a);return n}function ti({addVariant:t,config:r}){let i=r("darkMode",null),[e,n=".dark"]=Array.isArray(i)?i:[i];if(e==="variant"){let s;if(Array.isArray(n)||typeof n=="function"?s=n:typeof n=="string"&&(s=[n]),Array.isArray(s))for(let a of s)a===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):a.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));n=s}e===null||(e==="selector"?t("dark",`&:where(${n}, ${n} *)`):e==="media"?t("dark","@media (prefers-color-scheme: dark)"):e==="variant"?t("dark",n):e==="class"&&t("dark",`&:is(${n} *)`))}function ri(t){for(let[r,i]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])t.utilities.static(`bg-gradient-to-${r}`,()=>[l("--tw-gradient-position",`to ${i} in oklab`),l("background-image","linear-gradient(var(--tw-gradient-stops))")]);t.utilities.static("bg-left-top",()=>[l("background-position","left top")]),t.utilities.static("bg-right-top",()=>[l("background-position","right top")]),t.utilities.static("bg-left-bottom",()=>[l("background-position","left bottom")]),t.utilities.static("bg-right-bottom",()=>[l("background-position","right bottom")]),t.utilities.static("object-left-top",()=>[l("object-position","left top")]),t.utilities.static("object-right-top",()=>[l("object-position","right top")]),t.utilities.static("object-left-bottom",()=>[l("object-position","left bottom")]),t.utilities.static("object-right-bottom",()=>[l("object-position","right bottom")]),t.utilities.functional("max-w-screen",r=>{if(!r.value||r.value.kind==="arbitrary")return;let i=t.theme.resolve(r.value.value,["--breakpoint"]);if(i)return[l("max-width",i)]}),t.utilities.static("overflow-ellipsis",()=>[l("text-overflow","ellipsis")]),t.utilities.static("decoration-slice",()=>[l("-webkit-box-decoration-break","slice"),l("box-decoration-break","slice")]),t.utilities.static("decoration-clone",()=>[l("-webkit-box-decoration-break","clone"),l("box-decoration-break","clone")]),t.utilities.functional("flex-shrink",r=>{if(!r.modifier){if(!r.value)return[l("flex-shrink","1")];if(r.value.kind==="arbitrary")return[l("flex-shrink",r.value.value)];if(T(r.value.value))return[l("flex-shrink",r.value.value)]}}),t.utilities.functional("flex-grow",r=>{if(!r.modifier){if(!r.value)return[l("flex-grow","1")];if(r.value.kind==="arbitrary")return[l("flex-grow",r.value.value)];if(T(r.value.value))return[l("flex-grow",r.value.value)]}})}function ii(t,r){let i=t.theme.screens||{},e=r.variants.get("min")?.order??0,n=[];for(let[a,f]of Object.entries(i)){let h=function(v){r.variants.static(a,w=>{w.nodes=[j("@media",m,w.nodes)]},{order:v})};var s=h;let u=r.variants.get(a),c=r.theme.resolveValue(a,["--breakpoint"]);if(u&&c&&!r.theme.hasDefault(`--breakpoint-${a}`))continue;let g=!0;typeof f=="string"&&(g=!1);let m=In(f);g?n.push(h):h(e)}if(n.length!==0){for(let[,a]of r.variants.variants)a.order>e&&(a.order+=n.length);r.variants.compareFns=new Map(Array.from(r.variants.compareFns).map(([a,f])=>(a>e&&(a+=n.length),[a,f])));for(let[a,f]of n.entries())f(e+a+1)}}function In(t){return(Array.isArray(t)?t:[t]).map(i=>typeof i=="string"?{min:i}:i&&typeof i=="object"?i:null).map(i=>{if(i===null)return null;if("raw"in i)return i.raw;let e="";return i.max!==void 0&&(e+=`${i.max} >= `),e+="width",i.min!==void 0&&(e+=` >= ${i.min}`),`(${e})`}).filter(Boolean).join(", ")}function ni(t,r){let i=t.theme.aria||{},e=t.theme.supports||{},n=t.theme.data||{};if(Object.keys(i).length>0){let s=r.variants.get("aria"),a=s?.applyFn,f=s?.compounds;r.variants.functional("aria",(u,c)=>{let g=c.value;return g&&g.kind==="named"&&g.value in i?a?.(u,{...c,value:{kind:"arbitrary",value:i[g.value]}}):a?.(u,c)},{compounds:f})}if(Object.keys(e).length>0){let s=r.variants.get("supports"),a=s?.applyFn,f=s?.compounds;r.variants.functional("supports",(u,c)=>{let g=c.value;return g&&g.kind==="named"&&g.value in e?a?.(u,{...c,value:{kind:"arbitrary",value:e[g.value]}}):a?.(u,c)},{compounds:f})}if(Object.keys(n).length>0){let s=r.variants.get("data"),a=s?.applyFn,f=s?.compounds;r.variants.functional("data",(u,c)=>{let g=c.value;return g&&g.kind==="named"&&g.value in n?a?.(u,{...c,value:{kind:"arbitrary",value:n[g.value]}}):a?.(u,c)},{compounds:f})}}var Fn=/^[a-z]+$/;async function li({designSystem:t,base:r,ast:i,loadModule:e,sources:n}){let s=0,a=[],f=[];I(i,(m,{parent:h,replaceWith:v,context:w})=>{if(m.kind==="at-rule"){if(m.name==="@plugin"){if(h!==null)throw new Error("`@plugin` cannot be nested.");let A=m.params.slice(1,-1);if(A.length===0)throw new Error("`@plugin` must have a path.");let b={};for(let y of m.nodes??[]){if(y.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${te([y])}

\`@plugin\` options must be a flat list of declarations.`);if(y.value===void 0)continue;let S=y.value,E=K(S,",").map(P=>{if(P=P.trim(),P==="null")return null;if(P==="true")return!0;if(P==="false")return!1;if(Number.isNaN(Number(P))){if(P[0]==='"'&&P[P.length-1]==='"'||P[0]==="'"&&P[P.length-1]==="'")return P.slice(1,-1);if(P[0]==="{"&&P[P.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${te([y]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(P);return P});b[y.property]=E.length===1?E[0]:E}a.push([{id:A,base:w.base,reference:!!w.reference},Object.keys(b).length>0?b:null]),v([]),s|=4;return}if(m.name==="@config"){if(m.nodes.length>0)throw new Error("`@config` cannot have a body.");if(h!==null)throw new Error("`@config` cannot be nested.");f.push({id:m.params.slice(1,-1),base:w.base,reference:!!w.reference}),v([]),s|=4;return}}}),ri(t);let u=t.resolveThemeValue;if(t.resolveThemeValue=function(h,v){return h.startsWith("--")?u(h,v):(s|=oi({designSystem:t,base:r,ast:i,sources:n,configs:[],pluginDetails:[]}),t.resolveThemeValue(h,v))},!a.length&&!f.length)return 0;let[c,g]=await Promise.all([Promise.all(f.map(async({id:m,base:h,reference:v})=>{let w=await e(m,h,"config");return{path:m,base:w.base,config:w.module,reference:v}})),Promise.all(a.map(async([{id:m,base:h,reference:v},w])=>{let A=await e(m,h,"plugin");return{path:m,base:A.base,plugin:A.module,options:w,reference:v}}))]);return s|=oi({designSystem:t,base:r,ast:i,sources:n,configs:c,pluginDetails:g}),s}function oi({designSystem:t,base:r,ast:i,sources:e,configs:n,pluginDetails:s}){let a=0,u=[...s.map(b=>{if(!b.options)return{config:{plugins:[b.plugin]},base:b.base,reference:b.reference};if("__isOptionsFunction"in b.plugin)return{config:{plugins:[b.plugin(b.options)]},base:b.base,reference:b.reference};throw new Error(`The plugin "${b.path}" does not accept options`)}),...n],{resolvedConfig:c}=Ut(t,[{config:Xr(t.theme),base:r,reference:!0},...u,{config:{plugins:[ti]},base:r,reference:!0}]),{resolvedConfig:g,replacedThemeKeys:m}=Ut(t,u),h=t.resolveThemeValue;t.resolveThemeValue=function(y,S){if(y[0]==="-"&&y[1]==="-")return h(y,S);let E=w.theme(y,void 0);if(Array.isArray(E)&&E.length===2)return E[0];if(Array.isArray(E))return E.join(", ");if(typeof E=="string")return E};let v={designSystem:t,ast:i,resolvedConfig:c,featuresRef:{set current(b){a|=b}}},w=_t({...v,referenceMode:!1}),A;for(let{handler:b,reference:y}of c.plugins)y?(A||=_t({...v,referenceMode:!0}),b(A)):b(w);if(Rr(t,g,m),Yr(t,g,m),ni(g,t),ii(g,t),ei(g,t),!t.theme.prefix&&c.prefix){if(c.prefix.endsWith("-")&&(c.prefix=c.prefix.slice(0,-1),console.warn(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!Fn.test(c.prefix))throw new Error(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);t.theme.prefix=c.prefix}if(!t.important&&c.important===!0&&(t.important=!0),typeof c.important=="string"){let b=c.important;I(i,(y,{replaceWith:S,parent:E})=>{if(y.kind==="at-rule"&&!(y.name!=="@tailwind"||y.params!=="utilities"))return E?.kind==="rule"&&E.selector===b?2:(S(F(b,[y])),2)})}for(let b of c.blocklist)t.invalidCandidates.add(b);for(let b of c.content.files){if("raw"in b)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(b,null,2)}

This feature is not currently supported.`);let y=!1;b.pattern[0]=="!"&&(y=!0,b.pattern=b.pattern.slice(1)),e.push({...b,negated:y})}return a}var ai=/^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/;function pt(t){let r=t.indexOf("{");if(r===-1)return[t];let i=[],e=t.slice(0,r),n=t.slice(r),s=0,a=n.lastIndexOf("}");for(let m=0;m<n.length;m++){let h=n[m];if(h==="{")s++;else if(h==="}"&&(s--,s===0)){a=m;break}}if(a===-1)throw new Error(`The pattern \`${t}\` is not balanced.`);let f=n.slice(1,a),u=n.slice(a+1),c;zn(f)?c=Ln(f):c=K(f,","),c=c.flatMap(m=>pt(m));let g=pt(u);for(let m of g)for(let h of c)i.push(e+h+m);return i}function zn(t){return ai.test(t)}function Ln(t){let r=t.match(ai);if(!r)return[t];let[,i,e,n]=r,s=n?parseInt(n,10):void 0,a=[];if(/^-?\d+$/.test(i)&&/^-?\d+$/.test(e)){let f=parseInt(i,10),u=parseInt(e,10);if(s===void 0&&(s=f<=u?1:-1),s===0)throw new Error("Step cannot be zero in sequence expansion.");if(s>0)for(let c=f;c<=u;c+=s){let g=c.toString();a.push(g)}else for(let c=f;c>=u;c+=s){let g=c.toString();a.push(g)}}return a}var Mn=/^[a-z]+$/,Qe=(n=>(n[n.None=0]="None",n[n.AtProperty=1]="AtProperty",n[n.ColorMix=2]="ColorMix",n[n.All=3]="All",n))(Qe||{});function Wn(){throw new Error("No `loadModule` function provided to `compile`")}function Bn(){throw new Error("No `loadStylesheet` function provided to `compile`")}function qn(t){let r=0,i=null;for(let e of K(t," "))e==="reference"?r|=2:e==="inline"?r|=1:e==="default"?r|=4:e==="static"?r|=8:e.startsWith("prefix(")&&e.endsWith(")")&&(i=e.slice(7,-1));return[r,i]}var $e=(f=>(f[f.None=0]="None",f[f.AtApply=1]="AtApply",f[f.AtImport=2]="AtImport",f[f.JsPluginCompat=4]="JsPluginCompat",f[f.ThemeFunction=8]="ThemeFunction",f[f.Utilities=16]="Utilities",f[f.Variants=32]="Variants",f))($e||{});async function si(t,{base:r="",loadModule:i=Wn,loadStylesheet:e=Bn}={}){let n=0;t=[ne({base:r},t)],n|=await Pt(t,r,e);let s=null,a=new He,f=[],u=[],c=null,g=null,m=[],h=[],v=[],w=[],A=null;I(t,(y,{parent:S,replaceWith:E,context:P})=>{if(y.kind==="at-rule"){if(y.name==="@tailwind"&&(y.params==="utilities"||y.params.startsWith("utilities"))){if(g!==null){E([]);return}let _=K(y.params," ");for(let L of _)if(L.startsWith("source(")){let R=L.slice(7,-1);if(R==="none"){A=R;continue}if(R[0]==='"'&&R[R.length-1]!=='"'||R[0]==="'"&&R[R.length-1]!=="'"||R[0]!=="'"&&R[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");A={base:P.sourceBase??P.base,pattern:R.slice(1,-1)}}g=y,n|=16}if(y.name==="@utility"){if(S!==null)throw new Error("`@utility` cannot be nested.");if(y.nodes.length===0)throw new Error(`\`@utility ${y.params}\` is empty. Utilities should include at least one property.`);let _=wr(y);if(_===null)throw new Error(`\`@utility ${y.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);u.push(_)}if(y.name==="@source"){if(y.nodes.length>0)throw new Error("`@source` cannot have a body.");if(S!==null)throw new Error("`@source` cannot be nested.");let _=!1,L=!1,R=y.params;if(R[0]==="n"&&R.startsWith("not ")&&(_=!0,R=R.slice(4)),R[0]==="i"&&R.startsWith("inline(")&&(L=!0,R=R.slice(7,-1)),R[0]==='"'&&R[R.length-1]!=='"'||R[0]==="'"&&R[R.length-1]!=="'"||R[0]!=="'"&&R[0]!=='"')throw new Error("`@source` paths must be quoted.");let G=R.slice(1,-1);if(L){let z=_?w:v,B=K(G," ");for(let se of B)for(let xe of pt(se))z.push(xe)}else h.push({base:P.base,pattern:G,negated:_});E([]);return}if(y.name==="@variant"&&(S===null?y.nodes.length===0?y.name="@custom-variant":(I(y.nodes,_=>{if(_.kind==="at-rule"&&_.name==="@slot")return y.name="@custom-variant",2}),y.name==="@variant"&&m.push(y)):m.push(y)),y.name==="@custom-variant"){if(S!==null)throw new Error("`@custom-variant` cannot be nested.");E([]);let[_,L]=K(y.params," ");if(!ot.test(_))throw new Error(`\`@custom-variant ${_}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(y.nodes.length>0&&L)throw new Error(`\`@custom-variant ${_}\` cannot have both a selector and a body.`);if(y.nodes.length===0){if(!L)throw new Error(`\`@custom-variant ${_}\` has no selector or body.`);let R=K(L.slice(1,-1),",");if(R.length===0||R.some(B=>B.trim()===""))throw new Error(`\`@custom-variant ${_} (${R.join(",")})\` selector is invalid.`);let G=[],z=[];for(let B of R)B=B.trim(),B[0]==="@"?G.push(B):z.push(B);f.push(B=>{B.variants.static(_,se=>{let xe=[];z.length>0&&xe.push(F(z.join(", "),se.nodes));for(let Dt of G)xe.push(M(Dt,se.nodes));se.nodes=xe},{compounds:ye([...z,...G])})});return}else{f.push(R=>{R.variants.fromAst(_,y.nodes)});return}}if(y.name==="@media"){let _=K(y.params," "),L=[];for(let R of _)if(R.startsWith("source(")){let G=R.slice(7,-1);I(y.nodes,(z,{replaceWith:B})=>{if(z.kind==="at-rule"&&z.name==="@tailwind"&&z.params==="utilities")return z.params+=` source(${G})`,B([ne({sourceBase:P.base},[z])]),2})}else if(R.startsWith("theme(")){let G=R.slice(6,-1),z=G.includes("reference");I(y.nodes,B=>{if(B.kind!=="at-rule"){if(z)throw new Error('Files imported with `@import "\u2026" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "\u2026";` instead.');return 0}if(B.name==="@theme")return B.params+=" "+G,1})}else if(R.startsWith("prefix(")){let G=R.slice(7,-1);I(y.nodes,z=>{if(z.kind==="at-rule"&&z.name==="@theme")return z.params+=` prefix(${G})`,1})}else R==="important"?s=!0:R==="reference"?y.nodes=[ne({reference:!0},y.nodes)]:L.push(R);L.length>0?y.params=L.join(" "):_.length>0&&E(y.nodes)}if(y.name==="@theme"){let[_,L]=qn(y.params);if(P.reference&&(_|=2),L){if(!Mn.test(L))throw new Error(`The prefix "${L}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);a.prefix=L}return I(y.nodes,R=>{if(R.kind==="at-rule"&&R.name==="@keyframes")return a.addKeyframes(R),1;if(R.kind==="comment")return;if(R.kind==="declaration"&&R.property.startsWith("--")){a.add(he(R.property),R.value??"",_);return}let G=te([j(y.name,y.params,[R])]).split(`
`).map((z,B,se)=>`${B===0||B>=se.length-2?" ":">"} ${z}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${G}`)}),c?E([]):(c=F(":root, :host",[]),E([c])),1}}});let b=$r(a);if(s&&(b.important=s),w.length>0)for(let y of w)b.invalidCandidates.add(y);n|=await li({designSystem:b,base:r,ast:t,loadModule:i,sources:h});for(let y of f)y(b);for(let y of u)y(b);if(c){let y=[];for(let[E,P]of b.theme.entries())P.options&2||y.push(l(ue(E),P.value));let S=b.theme.getKeyframes();for(let E of S)t.push(ne({theme:!0},[D([E])]));c.nodes=[ne({theme:!0},y)]}if(g){let y=g;y.kind="context",y.context={}}if(m.length>0){for(let y of m){let S=F("&",y.nodes),E=y.params,P=b.parseVariant(E);if(P===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${E}`);if(Ve(S,P,b.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${E}`);Object.assign(y,S)}n|=32}return n|=Ne(t,b),n|=Ue(t,b),I(t,(y,{replaceWith:S})=>{if(y.kind==="at-rule")return y.name==="@utility"&&S([]),1}),{designSystem:b,ast:t,sources:h,root:A,utilitiesNode:g,features:n,inlineCandidates:v}}async function ui(t,r={}){let{designSystem:i,ast:e,sources:n,root:s,utilitiesNode:a,features:f,inlineCandidates:u}=await si(t,r);e.unshift(qe(`! tailwindcss v${jt} | MIT License | https://tailwindcss.com `));function c(w){i.invalidCandidates.add(w)}let g=new Set,m=null,h=0,v=!1;for(let w of u)i.invalidCandidates.has(w)||(g.add(w),v=!0);return{sources:n,root:s,features:f,build(w){if(f===0)return t;if(!a)return m??=ve(e,i,r.polyfills),m;let A=v,b=!1;v=!1;let y=g.size;for(let E of w)if(!i.invalidCandidates.has(E))if(E[0]==="-"&&E[1]==="-"){let P=i.theme.markUsedVariable(E);A||=P,b||=P}else g.add(E),A||=g.size!==y;if(!A)return m??=ve(e,i,r.polyfills),m;let S=pe(g,i,{onInvalidCandidate:c}).astNodes;return!b&&h===S.length?(m??=ve(e,i,r.polyfills),m):(h=S.length,a.nodes=S,m=ve(e,i,r.polyfills),m)}}}async function Hn(t,r={}){let i=ge(t),e=await ui(i,r),n=i,s=t;return{...e,build(a){let f=e.build(a);return f===n||(s=te(f),n=f),s}}}async function Gn(t,r={}){return(await si(ge(t),r)).designSystem}function ze(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}for(let t in dt)t!=="default"&&(ze[t]=dt[t]);module.exports=ze;
