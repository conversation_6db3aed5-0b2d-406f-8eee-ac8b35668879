// JavaScript for OMSY Website
console.log("OMSY script loaded.");

// Mobile Menu Toggle
const menuButton = document.getElementById('mobile-menu-button');
const mobileMenu = document.getElementById('mobile-menu');

if (menuButton && mobileMenu) {
    menuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
    });
} else {
    console.error("Mobile menu button or menu element not found!");
}

// Contact Form Validation
const contactForm = document.querySelector('form[action^="mailto:"]'); // Select form targeting mailto

if (contactForm) {
    contactForm.addEventListener('submit', function(event) {
        const nameInput = document.getElementById('name');
        const emailInput = document.getElementById('email');
        const subjectInput = document.getElementById('subject');
        const messageInput = document.getElementById('message');

        let missingFields = [];

        if (!nameInput || nameInput.value.trim() === '') {
            missingFields.push('Name');
        }
        if (!emailInput || emailInput.value.trim() === '') {
            missingFields.push('Email');
        }
        if (!subjectInput || subjectInput.value.trim() === '') {
            missingFields.push('Subject');
        }
        if (!messageInput || messageInput.value.trim() === '') {
            missingFields.push('Message');
        }

        if (missingFields.length > 0) {
            // Prevent the mailto link from opening if fields are missing
            event.preventDefault();
            alert('Please fill out the following required fields: ' + missingFields.join(', '));
        }
        // If all fields are filled, the default mailto action will proceed
    });
} else {
    // Try to find the form on pages other than contact if script runs globally
    // console.log("Contact form not found on this page.");
}